import type { RouteRecordRaw } from 'vue-router';

// 独立页面 没有导航栏和侧边栏
const routes: RouteRecordRaw[] = [
  {
    path: '/standalone/wechat-menu/:public_id',
    name: 'WechatMenu',
    component: () =>
      import('#/views/payment/wechat-public/standalone/wechat-menu.vue'),
    meta: {
      title: '公众号预览',
      noBasicLayout: true, // 关键属性：不使用基础布局
      hideInMenu: true, // 在菜单中隐藏
      hideInTab: true, // 在标签页中隐藏
      ignoreAccess: true, // 忽略权限检查
      query: {
        name: '',
        appid: '',
        user_name: '',
      },
    },
  },
  // 低代码 拖拽
  {
    path: '/standalone/designer',
    name: 'Designer',
    component: () => import('#/views/designer/index.vue'),
    meta: {
      icon: 'mdi:code-braces', // 图标，可选，根据需要添加
      title: '低代码',
      noBasicLayout: true, // 关键属性：不使用基础布局
      // hideInMenu: true, // 在菜单中隐藏
      // hideInTab: true, // 在标签页中隐藏
      ignoreAccess: true, // 忽略权限检查
    },
  },
];

export default routes;
