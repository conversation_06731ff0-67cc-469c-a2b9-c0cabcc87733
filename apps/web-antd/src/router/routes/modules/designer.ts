import type { RouteRecordRaw } from 'vue-router';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'mdi:design',
      order: 1, // 排在最前面
      title: '低代码设计器',
    },
    name: 'Designer',
    path: '/designer',
    redirect: '/designer/new',
    children: [
      {
        path: 'new',
        name: 'New<PERSON>esigner',
        component: () => import('#/views/designer/test-simple.vue'),
        meta: {
          title: '新版设计器',
          icon: 'mdi:design',
          keepAlive: true,
          noBasicLayout: true
        },
      },
      {
        path: 'legacy',
        name: 'LegacyDesigner',
        component: () => import('#/views/designer/index.vue'),
        meta: {
          title: '原版设计器',
          icon: 'mdi:design-outline',
          keepAlive: true,
          noBasicLayout: true
        },
      },


    ],
  },
];

export default routes;
