import { computed, reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import { nanoid } from 'nanoid';

// 页面信息接口
export interface PageInfo {
  id: string;
  title: string;
  path: string;
  description?: string;
}

// 组件信息接口
export interface ComponentInfo {
  id: string;
  type: string;
  props: Record<string, any>;
  style: Record<string, any>;
  children?: ComponentInfo[];
}

export function useNewEditor() {
  // 页面列表
  const pageList = ref<PageInfo[]>([
    { id: 'home', title: '首页', path: '/pages/home', description: '应用首页' },
    { id: 'search', title: '搜索', path: '/pages/search', description: '搜索页面' },
    { id: 'category', title: '分类', path: '/pages/category', description: '分类页面' },
    { id: 'cart', title: '购物车', path: '/pages/cart', description: '购物车页面' },
    { id: 'user', title: '我的', path: '/pages/user', description: '个人中心' },
  ]);

  // 当前编辑的页面ID
  const currentPageId = ref('home');

  // 存储每个页面的组件列表
  const pagesComponentsMap = reactive(new Map<string, ComponentInfo[]>());

  // 初始化页面组件映射
  function initPagesMap() {
    pageList.value.forEach((page) => {
      if (!pagesComponentsMap.has(page.id)) {
        pagesComponentsMap.set(page.id, []);
      }
    });
  }

  // 初始化
  initPagesMap();

  // 当前页面的组件列表
  const canvasComponents = computed({
    get: () => {
      return pagesComponentsMap.get(currentPageId.value) || [];
    },
    set: (value: ComponentInfo[]) => {
      pagesComponentsMap.set(currentPageId.value, value);
    },
  });

  // 切换页面
  function switchPage(pageId: string): boolean {
    const targetPage = pageList.value.find(p => p.id === pageId);
    if (!targetPage) {
      message.error('页面不存在');
      return false;
    }

    currentPageId.value = pageId;
    
    // 确保新页面有组件映射
    if (!pagesComponentsMap.has(pageId)) {
      pagesComponentsMap.set(pageId, []);
    }

    return true;
  }

  // 添加页面
  function addPage(page: PageInfo): boolean {
    // 检查页面ID是否已存在
    if (pageList.value.some(p => p.id === page.id)) {
      message.error('页面ID已存在');
      return false;
    }

    // 添加页面
    pageList.value.push(page);
    
    // 初始化页面组件映射
    pagesComponentsMap.set(page.id, []);

    return true;
  }

  // 更新页面
  function updatePage(updatedPage: PageInfo): boolean {
    const index = pageList.value.findIndex(p => p.id === updatedPage.id);
    if (index === -1) {
      message.error('页面不存在');
      return false;
    }

    // 更新页面信息
    pageList.value[index] = { ...updatedPage };
    return true;
  }

  // 删除页面
  function deletePage(pageId: string): boolean {
    if (pageList.value.length <= 1) {
      message.error('至少需要保留一个页面');
      return false;
    }

    const index = pageList.value.findIndex(p => p.id === pageId);
    if (index === -1) {
      message.error('页面不存在');
      return false;
    }

    // 删除页面
    pageList.value.splice(index, 1);
    
    // 删除页面组件映射
    pagesComponentsMap.delete(pageId);

    // 如果删除的是当前页面，切换到第一个页面
    if (currentPageId.value === pageId) {
      currentPageId.value = pageList.value[0].id;
    }

    return true;
  }

  // 添加组件到当前页面
  function addComponent(component: ComponentInfo): boolean {
    const currentComponents = canvasComponents.value;
    currentComponents.push(component);
    canvasComponents.value = [...currentComponents];
    return true;
  }

  // 更新组件
  function updateComponent(updatedComponent: ComponentInfo): boolean {
    const currentComponents = canvasComponents.value;
    const index = currentComponents.findIndex(c => c.id === updatedComponent.id);
    
    if (index === -1) {
      message.error('组件不存在');
      return false;
    }

    currentComponents[index] = { ...updatedComponent };
    canvasComponents.value = [...currentComponents];
    return true;
  }

  // 删除组件
  function deleteComponent(componentId: string): boolean {
    const currentComponents = canvasComponents.value;
    const index = currentComponents.findIndex(c => c.id === componentId);
    
    if (index === -1) {
      message.error('组件不存在');
      return false;
    }

    currentComponents.splice(index, 1);
    canvasComponents.value = [...currentComponents];
    return true;
  }

  // 清空当前页面的所有组件
  function clearCanvas(): boolean {
    canvasComponents.value = [];
    return true;
  }

  // 根据组件ID查找组件
  function findComponent(componentId: string): ComponentInfo | null {
    return canvasComponents.value.find(c => c.id === componentId) || null;
  }

  // 获取页面组件数量
  function getPageComponentCount(pageId: string): number {
    const components = pagesComponentsMap.get(pageId);
    return components ? components.length : 0;
  }

  // 复制页面
  function duplicatePage(sourcePage: PageInfo, newPageInfo: Partial<PageInfo>): boolean {
    const newPage: PageInfo = {
      id: newPageInfo.id || `${sourcePage.id}_copy_${Date.now()}`,
      title: newPageInfo.title || `${sourcePage.title} - 副本`,
      path: newPageInfo.path || `${sourcePage.path}_copy`,
      description: newPageInfo.description || sourcePage.description,
    };

    // 添加新页面
    if (!addPage(newPage)) {
      return false;
    }

    // 复制组件
    const sourceComponents = pagesComponentsMap.get(sourcePage.id) || [];
    const copiedComponents = sourceComponents.map(component => ({
      ...component,
      id: `${component.type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    }));

    pagesComponentsMap.set(newPage.id, copiedComponents);
    return true;
  }

  // 导出页面数据
  function exportPageData(pageId?: string) {
    if (pageId) {
      const page = pageList.value.find(p => p.id === pageId);
      const components = pagesComponentsMap.get(pageId) || [];
      return {
        page,
        components,
      };
    }

    // 导出所有页面数据
    const allPagesData = pageList.value.map(page => ({
      page,
      components: pagesComponentsMap.get(page.id) || [],
    }));

    return {
      pages: allPagesData,
      currentPageId: currentPageId.value,
    };
  }

  // 导入页面数据
  function importPageData(data: any): boolean {
    try {
      if (data.pages) {
        // 导入多个页面
        pageList.value = data.pages.map((item: any) => item.page);
        pagesComponentsMap.clear();
        
        data.pages.forEach((item: any) => {
          pagesComponentsMap.set(item.page.id, item.components || []);
        });

        if (data.currentPageId) {
          currentPageId.value = data.currentPageId;
        }
      } else if (data.page && data.components) {
        // 导入单个页面
        if (!addPage(data.page)) {
          return false;
        }
        pagesComponentsMap.set(data.page.id, data.components);
      }

      return true;
    } catch (error) {
      console.error('导入页面数据失败:', error);
      message.error('导入页面数据失败');
      return false;
    }
  }

  return {
    // 状态
    pageList,
    currentPageId,
    pagesComponentsMap,
    canvasComponents,

    // 页面管理方法
    switchPage,
    addPage,
    updatePage,
    deletePage,
    duplicatePage,

    // 组件管理方法
    addComponent,
    updateComponent,
    deleteComponent,
    clearCanvas,
    findComponent,

    // 工具方法
    getPageComponentCount,
    exportPageData,
    importPageData,
  };
}
