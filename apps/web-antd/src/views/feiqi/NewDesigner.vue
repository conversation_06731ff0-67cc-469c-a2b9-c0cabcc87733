<template>
  <div class="new-designer-container">
    <!-- 顶部导航栏 -->
    <TopNavbar
      :current-page-id="currentPageId"
      :page-list="pageList"
      :can-undo="canUndo"
      :can-redo="canRedo"
      @tabbar-config="handleTabBarConfig"
      @global-nav-config="handleGlobalNavConfig"
      @undo="handleUndo"
      @redo="handleRedo"
      @preview="handlePreview"
      @view-code="handleViewCode"
      @save="handleSave"
      @publish="handlePublish"
      @tabbar-config-save="handleTabBarConfigSave"
    />

    <div class="designer-main">
      <!-- 左侧二级菜单 -->
      <SidebarMenu
        :page-list="pageList"
        :current-page-id="currentPageId"
        @page-switch="handlePageSwitch"
        @page-add="handlePageAdd"
        @page-edit="handlePageEdit"
        @page-duplicate="handlePageDuplicate"
        @page-delete="handlePageDelete"
      />

      <!-- 中央页面编辑器 -->
      <PageEditor
        :page-info="currentPageInfo"
        :components="canvasComponents"
        :selected-component="selectedComponent"
        @component-add="handleComponentAdd"
        @component-select="handleComponentSelect"
        @component-update="handleComponentUpdate"
        @component-delete="handleComponentDelete"
        @canvas-clear="handleCanvasClear"
        @page-switch="handlePageSwitch"
        @navigate="handleNavigate"
      />
    </div>

    <!-- 页面管理弹窗 -->
    <EnhancedPageManager
      v-model:visible="showPageManager"
      :page-list="pageList"
      :current-page-id="currentPageId"
      :pages-components-map="pagesComponentsMap"
      @page-switch="handlePageSwitch"
      @page-add="handlePageAddFromManager"
      @page-edit="handlePageEditFromManager"
      @page-duplicate="handlePageDuplicateFromManager"
      @page-delete="handlePageDeleteFromManager"
    />

    <!-- 预览抽屉 -->
    <PagePreviewDrawer
      v-model:visible="showPreview"
      :page-list="pageList"
      :current-page-id="currentPageId"
      :pages-components-map="pagesComponentsMap"
    />

    <!-- 代码预览弹窗 -->
    <CodePreviewModal
      v-model:visible="showCodePreview"
      :page-list="pageList"
      :pages-components-map="pagesComponentsMap"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive } from 'vue';
import { message, Modal } from 'ant-design-vue';

// 导入组件
import TopNavbar from './components/TopNavbar.vue';
import SidebarMenu from './components/SidebarMenu.vue';
import PageEditor from './components/PageEditor.vue';
import EnhancedPageManager from './components/EnhancedPageManager.vue';
import PagePreviewDrawer from './components/PagePreviewDrawer.vue';
import CodePreviewModal from './components/CodePreviewModal.vue';

// 导入hooks
import { useNewEditor } from './hooks/useNewEditor';
import { useHistory } from './hooks/useHistory';

// 页面信息接口
interface PageInfo {
  id: string;
  title: string;
  path: string;
  description?: string;
}

// 组件信息接口
interface ComponentInfo {
  id: string;
  type: string;
  props: Record<string, any>;
  style: Record<string, any>;
  children?: ComponentInfo[];
}

// 使用编辑器hooks
const {
  pageList,
  currentPageId,
  pagesComponentsMap,
  canvasComponents,
  switchPage,
  addPage,
  deletePage,
  updatePage,
  addComponent,
  updateComponent,
  deleteComponent,
  clearCanvas,
} = useNewEditor();

// 使用历史记录hooks
const {
  canUndo,
  canRedo,
  undo,
  redo,
  saveState,
} = useHistory();

// 选中的组件
const selectedComponent = ref<ComponentInfo | null>(null);

// 弹窗状态
const showPageManager = ref(false);
const showPreview = ref(false);
const showCodePreview = ref(false);

// 当前页面信息
const currentPageInfo = computed(() => {
  return pageList.value.find(page => page.id === currentPageId.value) || {
    id: 'unknown',
    title: '未知页面',
    path: '/unknown',
  };
});

// 全局TabBar配置
const globalTabBarConfig = reactive({
  enabled: false,
  position: 'bottom' as 'bottom' | 'top' | 'side',
  style: 'default' as 'default' | 'card' | 'line',
  tabs: [] as any[],
});

// 页面切换处理
function handlePageSwitch(pageId: string) {
  const success = switchPage(pageId);
  if (success) {
    selectedComponent.value = null;
    message.success(`已切换到页面：${currentPageInfo.value.title}`);
  }
}

// 页面添加处理
function handlePageAdd() {
  showPageManager.value = true;
}

// 页面编辑处理
function handlePageEdit(page: PageInfo) {
  showPageManager.value = true;
}

// 页面复制处理
function handlePageDuplicate(page: PageInfo) {
  const newPage = {
    ...page,
    id: `${page.id}_copy_${Date.now()}`,
    title: `${page.title} - 副本`,
    path: `${page.path}_copy`,
  };
  addPage(newPage);
  message.success('页面复制成功');
}

// 页面删除处理
function handlePageDelete(pageId: string) {
  if (pageList.value.length <= 1) {
    message.warning('至少需要保留一个页面');
    return;
  }
  
  Modal.confirm({
    title: '确认删除',
    content: '确定要删除这个页面吗？删除后无法恢复。',
    onOk() {
      deletePage(pageId);
      message.success('页面删除成功');
    },
  });
}

// 从页面管理器添加页面
function handlePageAddFromManager(page: PageInfo) {
  addPage(page);
  message.success('页面创建成功');
}

// 从页面管理器编辑页面
function handlePageEditFromManager(page: PageInfo) {
  updatePage(page);
  message.success('页面更新成功');
}

// 从页面管理器复制页面
function handlePageDuplicateFromManager(page: PageInfo) {
  addPage(page);
  message.success('页面复制成功');
}

// 从页面管理器删除页面
function handlePageDeleteFromManager(pageId: string) {
  deletePage(pageId);
  message.success('页面删除成功');
}

// 组件添加处理
function handleComponentAdd(component: ComponentInfo) {
  addComponent(component);
  saveState();
  message.success('组件添加成功');
}

// 组件选择处理
function handleComponentSelect(component: ComponentInfo) {
  selectedComponent.value = component;
}

// 组件更新处理
function handleComponentUpdate(component: ComponentInfo) {
  updateComponent(component);
  saveState();
}

// 组件删除处理
function handleComponentDelete(componentId: string) {
  deleteComponent(componentId);
  selectedComponent.value = null;
  saveState();
  message.success('组件删除成功');
}

// 清空画布处理
function handleCanvasClear() {
  Modal.confirm({
    title: '确认清空',
    content: '确定要清空当前页面的所有组件吗？',
    onOk() {
      clearCanvas();
      selectedComponent.value = null;
      saveState();
      message.success('画布已清空');
    },
  });
}

// 导航处理
function handleNavigate(path: string) {
  console.log('导航到:', path);
}

// TabBar配置处理
function handleTabBarConfig() {
  console.log('TabBar配置');
}

// 全局导航配置处理
function handleGlobalNavConfig() {
  console.log('全局导航配置');
}

// 撤销处理
function handleUndo() {
  undo();
  selectedComponent.value = null;
}

// 重做处理
function handleRedo() {
  redo();
  selectedComponent.value = null;
}

// 预览处理
function handlePreview() {
  showPreview.value = true;
}

// 查看代码处理
function handleViewCode() {
  showCodePreview.value = true;
}

// 保存处理
function handleSave() {
  // 这里可以实现保存到服务器的逻辑
  message.success('保存成功');
}

// 发布处理
function handlePublish() {
  // 这里可以实现发布的逻辑
  message.success('发布成功');
}

// TabBar配置保存处理
function handleTabBarConfigSave(config: any) {
  Object.assign(globalTabBarConfig, config);
  message.success('TabBar配置保存成功');
}
</script>

<style scoped>
.new-designer-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.designer-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}
</style>
