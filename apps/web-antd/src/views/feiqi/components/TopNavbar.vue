<template>
  <div class="top-navbar">
    <div class="navbar-left">
      <div class="project-info">
        <h2 class="project-title">低代码设计器</h2>
        <div class="current-page-info">
          <MdiFileDocument class="page-icon" />
          <span class="page-name">{{ currentPageTitle }}</span>
        </div>
      </div>
    </div>

    <div class="navbar-center">
      <div class="navigation-config">
        <div class="config-section">
          <span class="section-label">全局导航配置</span>
          <div class="config-actions">
            <Button 
              type="default" 
              size="small"
              @click="handleTabBarConfig"
            >
              <MdiNavigation class="btn-icon" />
              TabBar配置
            </Button>
            <Button 
              type="default" 
              size="small"
              @click="handleGlobalNavConfig"
            >
              <MdiCog class="btn-icon" />
              导航设置
            </Button>
          </div>
        </div>
      </div>
    </div>

    <div class="navbar-right">
      <div class="toolbar">
        <Tooltip title="撤销">
          <Button 
            type="text" 
            :disabled="!canUndo"
            @click="handleUndo"
          >
            <MdiUndo class="tool-icon" />
          </Button>
        </Tooltip>
        
        <Tooltip title="重做">
          <Button 
            type="text" 
            :disabled="!canRedo"
            @click="handleRedo"
          >
            <MdiRedo class="tool-icon" />
          </Button>
        </Tooltip>

        <Divider type="vertical" />

        <Tooltip title="预览">
          <Button 
            type="text"
            @click="handlePreview"
          >
            <MdiEye class="tool-icon" />
          </Button>
        </Tooltip>

        <Tooltip title="代码">
          <Button 
            type="text"
            @click="handleViewCode"
          >
            <MdiCodeBraces class="tool-icon" />
          </Button>
        </Tooltip>

        <Divider type="vertical" />

        <Tooltip title="保存">
          <Button 
            type="primary" 
            size="small"
            @click="handleSave"
          >
            <MdiContentSave class="btn-icon" />
            保存
          </Button>
        </Tooltip>

        <Tooltip title="发布">
          <Button 
            type="primary" 
            size="small"
            @click="handlePublish"
          >
            <MdiRocketLaunch class="btn-icon" />
            发布
          </Button>
        </Tooltip>
      </div>
    </div>

    <!-- TabBar配置弹窗 -->
    <Modal
      v-model:open="showTabBarConfig"
      title="全局TabBar配置"
      width="800px"
      @ok="handleTabBarConfigSave"
      @cancel="handleTabBarConfigCancel"
    >
      <div class="tabbar-config-content">
        <div class="config-form">
          <div class="form-item">
            <label>启用全局TabBar</label>
            <Switch v-model:checked="globalTabBarConfig.enabled" />
          </div>
          
          <template v-if="globalTabBarConfig.enabled">
            <div class="form-item">
              <label>TabBar位置</label>
              <Select v-model:value="globalTabBarConfig.position" style="width: 200px">
                <SelectOption value="bottom">底部</SelectOption>
                <SelectOption value="top">顶部</SelectOption>
                <SelectOption value="side">侧边</SelectOption>
              </Select>
            </div>

            <div class="form-item">
              <label>TabBar样式</label>
              <div class="style-options">
                <Radio.Group v-model:value="globalTabBarConfig.style">
                  <Radio value="default">默认</Radio>
                  <Radio value="card">卡片</Radio>
                  <Radio value="line">线条</Radio>
                </Radio.Group>
              </div>
            </div>

            <div class="form-item">
              <label>Tab项配置</label>
              <div class="tabs-config">
                <div
                  v-for="(tab, index) in globalTabBarConfig.tabs"
                  :key="index"
                  class="tab-config-item"
                >
                  <Input 
                    v-model:value="tab.title" 
                    placeholder="标签标题"
                    style="width: 120px"
                  />
                  <Input 
                    v-model:value="tab.icon" 
                    placeholder="图标名称"
                    style="width: 120px"
                  />
                  <Select 
                    v-model:value="tab.pageId" 
                    placeholder="绑定页面"
                    style="width: 120px"
                  >
                    <SelectOption 
                      v-for="page in pageList" 
                      :key="page.id" 
                      :value="page.id"
                    >
                      {{ page.title }}
                    </SelectOption>
                  </Select>
                  <Button 
                    type="text" 
                    danger
                    @click="removeTab(index)"
                  >
                    <MdiDelete />
                  </Button>
                </div>
                
                <Button 
                  type="dashed" 
                  @click="addTab"
                  style="width: 100%"
                >
                  <MdiPlus />
                  添加Tab
                </Button>
              </div>
            </div>
          </template>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { 
  Button, 
  Tooltip, 
  Divider, 
  Modal, 
  Switch, 
  Select, 
  Radio, 
  Input 
} from 'ant-design-vue';
import {
  MdiFileDocument,
  MdiNavigation,
  MdiCog,
  MdiUndo,
  MdiRedo,
  MdiEye,
  MdiCodeBraces,
  MdiContentSave,
  MdiRocketLaunch,
  MdiDelete,
  MdiPlus,
} from '@vben/icons';

const { SelectOption } = Select;

interface PageInfo {
  id: string;
  title: string;
  path: string;
}

interface TabConfig {
  title: string;
  icon: string;
  pageId: string;
}

interface GlobalTabBarConfig {
  enabled: boolean;
  position: 'bottom' | 'top' | 'side';
  style: 'default' | 'card' | 'line';
  tabs: TabConfig[];
}

interface Props {
  currentPageId: string;
  pageList: PageInfo[];
  canUndo: boolean;
  canRedo: boolean;
}

interface Emits {
  (e: 'tabbar-config'): void;
  (e: 'global-nav-config'): void;
  (e: 'undo'): void;
  (e: 'redo'): void;
  (e: 'preview'): void;
  (e: 'view-code'): void;
  (e: 'save'): void;
  (e: 'publish'): void;
  (e: 'tabbar-config-save', config: GlobalTabBarConfig): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 当前页面标题
const currentPageTitle = computed(() => {
  const page = props.pageList.find(p => p.id === props.currentPageId);
  return page?.title || '未知页面';
});

// TabBar配置相关
const showTabBarConfig = ref(false);
const globalTabBarConfig = ref<GlobalTabBarConfig>({
  enabled: false,
  position: 'bottom',
  style: 'default',
  tabs: [
    { title: '首页', icon: 'mdi:home', pageId: 'home' },
    { title: '搜索', icon: 'mdi:magnify', pageId: 'search' },
    { title: '我的', icon: 'mdi:account', pageId: 'user' },
  ],
});

// 处理TabBar配置
function handleTabBarConfig() {
  showTabBarConfig.value = true;
}

// 处理全局导航配置
function handleGlobalNavConfig() {
  emit('global-nav-config');
}

// 处理撤销
function handleUndo() {
  emit('undo');
}

// 处理重做
function handleRedo() {
  emit('redo');
}

// 处理预览
function handlePreview() {
  emit('preview');
}

// 处理查看代码
function handleViewCode() {
  emit('view-code');
}

// 处理保存
function handleSave() {
  emit('save');
}

// 处理发布
function handlePublish() {
  emit('publish');
}

// 添加Tab
function addTab() {
  globalTabBarConfig.value.tabs.push({
    title: `标签${globalTabBarConfig.value.tabs.length + 1}`,
    icon: 'mdi:circle',
    pageId: '',
  });
}

// 删除Tab
function removeTab(index: number) {
  globalTabBarConfig.value.tabs.splice(index, 1);
}

// 保存TabBar配置
function handleTabBarConfigSave() {
  emit('tabbar-config-save', globalTabBarConfig.value);
  showTabBarConfig.value = false;
}

// 取消TabBar配置
function handleTabBarConfigCancel() {
  showTabBarConfig.value = false;
}
</script>

<style scoped>
.top-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
  padding: 0 24px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

.navbar-left {
  display: flex;
  align-items: center;
}

.project-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.project-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.current-page-info {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  background: #f5f5f5;
  border-radius: 16px;
}

.page-icon {
  width: 14px;
  height: 14px;
  color: #666;
}

.page-name {
  font-size: 13px;
  color: #666;
}

.navbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.navigation-config {
  display: flex;
  align-items: center;
}

.config-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.section-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.config-actions {
  display: flex;
  gap: 8px;
}

.btn-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

.navbar-right {
  display: flex;
  align-items: center;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 4px;
}

.tool-icon {
  width: 16px;
  height: 16px;
}

.tabbar-config-content {
  padding: 16px 0;
}

.config-form {
  space-y: 16px;
}

.form-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
}

.form-item label {
  width: 120px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  line-height: 32px;
}

.style-options {
  display: flex;
  align-items: center;
}

.tabs-config {
  flex: 1;
  space-y: 8px;
}

.tab-config-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}
</style>
