<template>
  <div class="page-editor">
    <!-- 左侧组件库 -->
    <div class="component-library">
      <div class="library-header">
        <h3>组件库</h3>
      </div>
      
      <div class="component-categories">
        <div
          v-for="category in componentCategories"
          :key="category.key"
          class="category-section"
        >
          <div class="category-header">
            <h4>{{ category.title }}</h4>
          </div>
          
          <div class="component-list">
            <div
              v-for="component in category.components"
              :key="component.type"
              class="component-item"
              draggable="true"
              @dragstart="handleDragStart($event, component)"
            >
              <div class="component-icon">
                <component :is="component.icon" />
              </div>
              <span class="component-name">{{ component.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 中央画布区域 -->
    <div class="canvas-area">
      <div class="canvas-header">
        <div class="page-info">
          <h3>{{ pageInfo.title }}</h3>
          <span class="page-path">{{ pageInfo.path }}</span>
        </div>
        
        <div class="canvas-tools">
          <Button size="small" @click="handleClearCanvas">
            <MdiTrashCan class="btn-icon" />
            清空画布
          </Button>
        </div>
      </div>

      <div 
        class="canvas-container"
        @drop="handleDrop"
        @dragover="handleDragOver"
      >
        <div class="canvas-phone">
          <div class="phone-header">
            <div class="status-bar">
              <span class="time">9:41</span>
              <div class="indicators">
                <MdiSignal class="indicator" />
                <MdiWifi class="indicator" />
                <MdiBattery class="indicator" />
              </div>
            </div>
          </div>
          
          <div class="phone-content">
            <div 
              class="canvas-content"
              :class="{ 'empty': components.length === 0 }"
            >
              <div v-if="components.length === 0" class="empty-canvas">
                <MdiDragVariant class="empty-icon" />
                <p>从左侧拖拽组件到此处开始设计</p>
              </div>
              
              <div v-else class="components-container">
                <ComponentRenderer
                  v-for="component in components"
                  :key="component.id"
                  :id="component.id"
                  :type="component.type"
                  :component-props="component.props"
                  :style="component.style"
                  :children="component.children"
                  @click="handleComponentClick(component)"
                  @pageSwitch="handlePageSwitch"
                  @navigate="handleNavigate"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧属性面板 -->
    <div class="properties-panel">
      <div class="panel-header">
        <h3>属性配置</h3>
      </div>
      
      <div class="panel-content">
        <div v-if="!selectedComponent" class="no-selection">
          <MdiCursorDefault class="no-selection-icon" />
          <p>请选择一个组件进行配置</p>
        </div>
        
        <div v-else class="component-properties">
          <div class="property-header">
            <h4>{{ selectedComponent.type }}</h4>
            <Button 
              type="text" 
              danger 
              size="small"
              @click="handleDeleteComponent"
            >
              <MdiDelete />
            </Button>
          </div>
          
          <div class="property-form">
            <!-- 这里会根据组件类型动态渲染属性表单 -->
            <component
              :is="getPropertyComponent(selectedComponent.type)"
              v-if="getPropertyComponent(selectedComponent.type)"
              :component="selectedComponent"
              @update="handleComponentUpdate"
            />
            
            <!-- 通用样式配置 -->
            <div class="style-config">
              <h5>样式配置</h5>
              <div class="style-form">
                <div class="form-row">
                  <label>宽度</label>
                  <Input 
                    v-model:value="selectedComponent.style.width"
                    placeholder="auto"
                    size="small"
                  />
                </div>
                <div class="form-row">
                  <label>高度</label>
                  <Input 
                    v-model:value="selectedComponent.style.height"
                    placeholder="auto"
                    size="small"
                  />
                </div>
                <div class="form-row">
                  <label>边距</label>
                  <Input 
                    v-model:value="selectedComponent.style.margin"
                    placeholder="0"
                    size="small"
                  />
                </div>
                <div class="form-row">
                  <label>内边距</label>
                  <Input 
                    v-model:value="selectedComponent.style.padding"
                    placeholder="0"
                    size="small"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Button, Input } from 'ant-design-vue';
import {
  MdiTrashCan,
  MdiSignal,
  MdiWifi,
  MdiBattery,
  MdiDragVariant,
  MdiCursorDefault,
  MdiDelete,
  MdiViewGrid,
  MdiText,
  MdiImage,
  MdiButton,
  MdiFormTextbox,
  MdiNavigation,
} from '@vben/icons';
import { ComponentRenderer } from '@vben/lowcode/core';

interface PageInfo {
  id: string;
  title: string;
  path: string;
}

interface ComponentInfo {
  id: string;
  type: string;
  props: Record<string, any>;
  style: Record<string, any>;
  children?: ComponentInfo[];
}

interface ComponentDefinition {
  type: string;
  name: string;
  icon: any;
}

interface Props {
  pageInfo: PageInfo;
  components: ComponentInfo[];
  selectedComponent?: ComponentInfo;
}

interface Emits {
  (e: 'component-add', component: ComponentInfo): void;
  (e: 'component-select', component: ComponentInfo): void;
  (e: 'component-update', component: ComponentInfo): void;
  (e: 'component-delete', componentId: string): void;
  (e: 'canvas-clear'): void;
  (e: 'page-switch', pageId: string): void;
  (e: 'navigate', path: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 组件分类
const componentCategories = [
  {
    key: 'basic',
    title: '基础组件',
    components: [
      { type: 'Text', name: '文本', icon: MdiText },
      { type: 'Button', name: '按钮', icon: MdiButton },
      { type: 'Image', name: '图片', icon: MdiImage },
      { type: 'Input', name: '输入框', icon: MdiFormTextbox },
    ],
  },
  {
    key: 'layout',
    title: '布局组件',
    components: [
      { type: 'Container', name: '容器', icon: MdiViewGrid },
    ],
  },
  {
    key: 'navigation',
    title: '导航组件',
    components: [
      { type: 'TabBar', name: '底部导航', icon: MdiNavigation },
      { type: 'TopTabBar', name: '顶部导航', icon: MdiNavigation },
      { type: 'SideTabBar', name: '侧边导航', icon: MdiNavigation },
    ],
  },
];

// 拖拽开始
function handleDragStart(event: DragEvent, component: ComponentDefinition) {
  if (event.dataTransfer) {
    event.dataTransfer.setData('component-type', component.type);
  }
}

// 拖拽悬停
function handleDragOver(event: DragEvent) {
  event.preventDefault();
}

// 拖拽放置
function handleDrop(event: DragEvent) {
  event.preventDefault();
  const componentType = event.dataTransfer?.getData('component-type');
  
  if (componentType) {
    const newComponent: ComponentInfo = {
      id: `${componentType}_${Date.now()}`,
      type: componentType,
      props: {},
      style: {},
    };
    
    emit('component-add', newComponent);
  }
}

// 组件点击
function handleComponentClick(component: ComponentInfo) {
  emit('component-select', component);
}

// 组件更新
function handleComponentUpdate(component: ComponentInfo) {
  emit('component-update', component);
}

// 删除组件
function handleDeleteComponent() {
  if (props.selectedComponent) {
    emit('component-delete', props.selectedComponent.id);
  }
}

// 清空画布
function handleClearCanvas() {
  emit('canvas-clear');
}

// 页面切换
function handlePageSwitch(pageId: string) {
  emit('page-switch', pageId);
}

// 导航
function handleNavigate(path: string) {
  emit('navigate', path);
}

// 获取属性配置组件
function getPropertyComponent(componentType: string) {
  // 这里可以根据组件类型返回对应的属性配置组件
  // 暂时返回null，后续可以扩展
  return null;
}
</script>

<style scoped>
.page-editor {
  display: flex;
  height: 100%;
  background: #f5f5f5;
}

.component-library {
  width: 280px;
  background: #fff;
  border-right: 1px solid #e8e8e8;
  overflow-y: auto;
}

.library-header {
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.library-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.component-categories {
  padding: 16px;
}

.category-section {
  margin-bottom: 24px;
}

.category-header h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.component-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.component-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s;
}

.component-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24,144,255,0.2);
}

.component-item:active {
  cursor: grabbing;
}

.component-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
  color: #666;
}

.component-name {
  font-size: 12px;
  color: #333;
  text-align: center;
}

.canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
}

.page-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.page-path {
  font-size: 12px;
  color: #999;
}

.btn-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

.canvas-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px;
}

.canvas-phone {
  width: 375px;
  height: 667px;
  background: #000;
  border-radius: 24px;
  padding: 8px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

.phone-header {
  height: 44px;
  background: #000;
  border-radius: 16px 16px 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 16px;
  color: #fff;
  font-size: 14px;
  font-weight: 600;
}

.indicators {
  display: flex;
  gap: 4px;
}

.indicator {
  width: 16px;
  height: 16px;
}

.phone-content {
  height: calc(100% - 44px);
  background: #fff;
  border-radius: 0 0 16px 16px;
  overflow: hidden;
}

.canvas-content {
  height: 100%;
  position: relative;
}

.canvas-content.empty {
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-canvas {
  text-align: center;
  color: #999;
}

.empty-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 12px;
}

.components-container {
  height: 100%;
  overflow-y: auto;
}

.properties-panel {
  width: 320px;
  background: #fff;
  border-left: 1px solid #e8e8e8;
  overflow-y: auto;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.panel-content {
  padding: 16px;
}

.no-selection {
  text-align: center;
  color: #999;
  padding: 48px 16px;
}

.no-selection-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 12px;
}

.property-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.property-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.style-config {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}

.style-config h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.style-form {
  space-y: 8px;
}

.form-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.form-row label {
  width: 60px;
  font-size: 12px;
  color: #666;
}
</style>
