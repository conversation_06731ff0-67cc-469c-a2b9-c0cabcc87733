<script setup lang="ts">
import { ref, computed } from 'vue';
import { Modal, Form, Input, Select, Button, Space, Card, Switch, InputNumber } from 'ant-design-vue';
import { MdiPlus, MdiDelete } from '@vben/icons';
import type { TabBarItem } from '../hooks/useEditor';

interface Props {
  visible: boolean;
  hasTabBar: boolean;
  tabBarItems: TabBarItem[];
  pageList: Array<{ id: string; title: string; path: string }>;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'update:hasTabBar', value: boolean): void;
  (e: 'update:tabBarItems', value: TabBarItem[]): void;
  (e: 'addItem'): void;
  (e: 'removeItem', id: string): void;
  (e: 'updateItem', id: string, updates: Partial<TabBarItem>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 本地状态
const localHasTabBar = ref(props.hasTabBar);
const localTabBarItems = ref<TabBarItem[]>([...props.tabBarItems]);

// 页面选项
const pageOptions = computed(() => {
  return props.pageList.map(page => ({
    label: page.title,
    value: page.id,
  }));
});

// 处理关闭
function handleCancel() {
  emit('update:visible', false);
  // 重置本地状态
  localHasTabBar.value = props.hasTabBar;
  localTabBarItems.value = [...props.tabBarItems];
}

// 处理确认
function handleOk() {
  emit('update:hasTabBar', localHasTabBar.value);
  emit('update:tabBarItems', localTabBarItems.value);
  emit('update:visible', false);
}

// 添加TabBar项
function addTabBarItem() {
  const newItem: TabBarItem = {
    id: `tab-${Date.now()}`,
    title: '新标签',
    pageId: props.pageList[0]?.id || '',
    icon: '',
  };
  localTabBarItems.value.push(newItem);
}

// 删除TabBar项
function removeTabBarItem(id: string) {
  localTabBarItems.value = localTabBarItems.value.filter(item => item.id !== id);
}

// 更新TabBar项
function updateTabBarItem(id: string, field: keyof TabBarItem, value: any) {
  const index = localTabBarItems.value.findIndex(item => item.id === id);
  if (index !== -1) {
    localTabBarItems.value[index] = {
      ...localTabBarItems.value[index],
      [field]: value,
    };
  }
}

// 获取iconify图标组件
function getIconComponent(iconName: string) {
  // 将 iconify 图标名称转换为组件名称
  // 例如: mdi:home -> MdiHome, lucide:home -> LucideHome
  if (!iconName.includes(':')) return null;

  const [prefix, name] = iconName.split(':');
  const componentName = prefix.charAt(0).toUpperCase() + prefix.slice(1) +
    name.split('-').map(part => part.charAt(0).toUpperCase() + part.slice(1)).join('');

  // 这里可以根据需要导入更多图标库
  // 目前主要支持 mdi 图标
  try {
    // 动态导入图标组件（这里简化处理，实际项目中可能需要更复杂的逻辑）
    return componentName;
  } catch {
    return null;
  }
}
</script>

<template>
  <Modal
    :open="visible"
    title="TabBar 配置"
    width="800px"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <div class="tabbar-config">
      <!-- 启用TabBar开关 -->
      <div class="config-section">
        <div class="section-title">基础设置</div>
        <Form layout="vertical">
          <Form.Item label="启用TabBar">
            <Switch 
              v-model:checked="localHasTabBar" 
              checked-children="开启" 
              un-checked-children="关闭" 
            />
            <div class="help-text">启用后，应用将显示底部TabBar导航</div>
          </Form.Item>
        </Form>
      </div>

      <!-- TabBar项配置 -->
      <div v-if="localHasTabBar" class="config-section">
        <div class="section-title">
          <span>Tab配置</span>
          <Button type="primary" size="small" @click="addTabBarItem">
            <MdiPlus />
            添加Tab
          </Button>
        </div>

        <div class="tabbar-items">
          <Card
            v-for="(item, index) in localTabBarItems"
            :key="item.id"
            size="small"
            class="tabbar-item-card"
          >
            <template #title>
              <span>Tab {{ index + 1 }}</span>
              <Button
                type="text"
                danger
                size="small"
                @click="removeTabBarItem(item.id)"
              >
                <MdiDelete />
              </Button>
            </template>

            <Form layout="vertical" size="small">
              <div class="form-row">
                <Form.Item label="标题" class="form-item">
                  <Input 
                    :value="item.title"
                    placeholder="请输入Tab标题"
                    @input="updateTabBarItem(item.id, 'title', $event.target.value)"
                  />
                </Form.Item>

                <Form.Item label="绑定页面" class="form-item">
                  <Select
                    :value="item.pageId"
                    placeholder="选择绑定的页面"
                    :options="pageOptions"
                    @change="updateTabBarItem(item.id, 'pageId', $event)"
                  />
                </Form.Item>
              </div>

              <div class="form-row">
                <Form.Item label="图标" class="form-item">
                  <Input
                    :value="item.icon"
                    placeholder="iconify图标名称，如：mdi:home"
                    @input="updateTabBarItem(item.id, 'icon', $event.target.value)"
                  />
                </Form.Item>

                <Form.Item label="徽标" class="form-item">
                  <Input 
                    :value="item.badge"
                    placeholder="徽标文字或数字"
                    @input="updateTabBarItem(item.id, 'badge', $event.target.value)"
                  />
                </Form.Item>
              </div>
            </Form>
          </Card>
        </div>

        <!-- 空状态 -->
        <div v-if="localTabBarItems.length === 0" class="empty-state">
          <p>暂无Tab配置</p>
          <Button type="dashed" @click="addTabBarItem">
            <MdiPlus />
            添加第一个Tab
          </Button>
        </div>
      </div>

      <!-- 预览区域 -->
      <div v-if="localHasTabBar && localTabBarItems.length > 0" class="config-section">
        <div class="section-title">预览</div>
        <div class="tabbar-preview">
          <div class="preview-phone">
            <div class="preview-content">
              <div class="preview-page">页面内容区域</div>
              <div class="preview-tabbar">
                <div
                  v-for="item in localTabBarItems"
                  :key="item.id"
                  class="preview-tab"
                >
                  <div class="preview-tab-icon">
                    <component
                      v-if="item.icon && item.icon.includes(':')"
                      :is="getIconComponent(item.icon)"
                      class="iconify-icon"
                    />
                    <img v-else-if="item.icon && item.icon.startsWith('http')" :src="item.icon" alt="" />
                    <div v-else class="default-icon">📱</div>
                  </div>
                  <div class="preview-tab-title">{{ item.title }}</div>
                  <div v-if="item.badge" class="preview-tab-badge">{{ item.badge }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
.tabbar-config {
  max-height: 600px;
  overflow-y: auto;
}

.config-section {
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
}

.help-text {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.tabbar-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.tabbar-item-card {
  border: 1px solid #d9d9d9;
}

.tabbar-item-card :deep(.ant-card-head) {
  padding: 8px 16px;
  min-height: auto;
}

.tabbar-item-card :deep(.ant-card-head-title) {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tabbar-item-card :deep(.ant-card-body) {
  padding: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-item {
  flex: 1;
  margin-bottom: 12px;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #999;
}

.tabbar-preview {
  display: flex;
  justify-content: center;
}

.preview-phone {
  width: 300px;
  height: 500px;
  border: 2px solid #333;
  border-radius: 20px;
  padding: 20px 10px;
  background: #000;
}

.preview-content {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
}

.preview-page {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 14px;
}

.preview-tabbar {
  height: 50px;
  border-top: 1px solid #eee;
  display: flex;
  background: #fff;
}

.preview-tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  font-size: 12px;
  color: #666;
}

.preview-tab-icon {
  margin-bottom: 2px;
}

.preview-tab-icon img {
  width: 20px;
  height: 20px;
}

.iconify-icon {
  width: 20px;
  height: 20px;
  font-size: 20px;
}

.default-icon {
  font-size: 16px;
}

.preview-tab-title {
  font-size: 10px;
}

.preview-tab-badge {
  position: absolute;
  top: 2px;
  right: 8px;
  background: #ff4d4f;
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 8px;
  min-width: 16px;
  text-align: center;
}
</style>
