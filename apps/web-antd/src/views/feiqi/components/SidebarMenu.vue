<template>
  <div class="sidebar-menu">
    <!-- 一级菜单 -->
    <div class="primary-menu">
      <div
        v-for="item in primaryMenuItems"
        :key="item.key"
        class="menu-item"
        :class="{ active: activeMenu === item.key }"
        @click="handleMenuClick(item.key)"
      >
        <component :is="item.icon" class="menu-icon" />
        <span class="menu-title">{{ item.title }}</span>
        <MdiChevronRight 
          class="expand-icon" 
          :class="{ expanded: activeMenu === item.key }" 
        />
      </div>
    </div>

    <!-- 二级菜单 -->
    <div class="secondary-menu" v-if="activeMenu">
      <div class="secondary-header">
        <h3>{{ getSecondaryTitle() }}</h3>
      </div>
      
      <!-- 导航管理子菜单 -->
      <div v-if="activeMenu === 'navigation'" class="navigation-menu">
        <div class="menu-section">
          <h4>页面导航</h4>
          <div class="page-list">
            <div
              v-for="page in pageList"
              :key="page.id"
              class="page-item"
              :class="{ active: currentPageId === page.id }"
              @click="handlePageSwitch(page.id)"
            >
              <MdiFileDocument class="page-icon" />
              <span class="page-title">{{ page.title }}</span>
              <div class="page-actions">
                <Tooltip title="编辑页面">
                  <MdiPencil 
                    class="action-btn" 
                    @click.stop="handleEditPage(page)"
                  />
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 页面管理子菜单 -->
      <div v-if="activeMenu === 'pages'" class="pages-menu">
        <div class="menu-section">
          <div class="section-header">
            <h4>页面列表</h4>
            <Button 
              type="primary" 
              size="small" 
              @click="handleAddPage"
            >
              <MdiPlus class="btn-icon" />
              新建页面
            </Button>
          </div>
          
          <div class="page-management-list">
            <div
              v-for="page in pageList"
              :key="page.id"
              class="page-management-item"
            >
              <div class="page-info">
                <MdiFileDocument class="page-icon" />
                <div class="page-details">
                  <div class="page-name">{{ page.title }}</div>
                  <div class="page-path">{{ page.path }}</div>
                </div>
              </div>
              
              <div class="page-actions">
                <Tooltip title="编辑">
                  <MdiPencil 
                    class="action-btn" 
                    @click="handleEditPage(page)"
                  />
                </Tooltip>
                <Tooltip title="复制">
                  <MdiContentDuplicate 
                    class="action-btn" 
                    @click="handleDuplicatePage(page)"
                  />
                </Tooltip>
                <Tooltip title="删除">
                  <MdiDelete 
                    class="action-btn delete-btn" 
                    @click="handleDeletePage(page.id)"
                  />
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { Button, Tooltip } from 'ant-design-vue';
import {
  MdiNavigation,
  MdiFileMultiple,
  MdiChevronRight,
  MdiFileDocument,
  MdiPencil,
  MdiPlus,
  MdiContentDuplicate,
  MdiDelete,
} from '@vben/icons';

interface PageInfo {
  id: string;
  title: string;
  path: string;
}

interface Props {
  pageList: PageInfo[];
  currentPageId: string;
}

interface Emits {
  (e: 'page-switch', pageId: string): void;
  (e: 'page-add'): void;
  (e: 'page-edit', page: PageInfo): void;
  (e: 'page-duplicate', page: PageInfo): void;
  (e: 'page-delete', pageId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 当前激活的一级菜单
const activeMenu = ref<string>('navigation');

// 一级菜单项
const primaryMenuItems = [
  {
    key: 'navigation',
    title: '导航管理',
    icon: MdiNavigation,
  },
  {
    key: 'pages',
    title: '页面管理',
    icon: MdiFileMultiple,
  },
];

// 处理一级菜单点击
function handleMenuClick(key: string) {
  activeMenu.value = activeMenu.value === key ? '' : key;
}

// 获取二级菜单标题
function getSecondaryTitle() {
  const item = primaryMenuItems.find(item => item.key === activeMenu.value);
  return item?.title || '';
}

// 处理页面切换
function handlePageSwitch(pageId: string) {
  emit('page-switch', pageId);
}

// 处理添加页面
function handleAddPage() {
  emit('page-add');
}

// 处理编辑页面
function handleEditPage(page: PageInfo) {
  emit('page-edit', page);
}

// 处理复制页面
function handleDuplicatePage(page: PageInfo) {
  emit('page-duplicate', page);
}

// 处理删除页面
function handleDeletePage(pageId: string) {
  emit('page-delete', pageId);
}
</script>

<style scoped>
.sidebar-menu {
  display: flex;
  height: 100%;
  background: #fff;
  border-right: 1px solid #e8e8e8;
}

.primary-menu {
  width: 200px;
  background: #fafafa;
  border-right: 1px solid #e8e8e8;
  padding: 16px 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 1px solid #f0f0f0;
}

.menu-item:hover {
  background: #f0f0f0;
}

.menu-item.active {
  background: #e6f7ff;
  border-right: 3px solid #1890ff;
}

.menu-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #666;
}

.menu-title {
  flex: 1;
  font-size: 14px;
  color: #333;
}

.expand-icon {
  width: 14px;
  height: 14px;
  color: #999;
  transition: transform 0.2s;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.secondary-menu {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.secondary-header h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.menu-section {
  margin-bottom: 24px;
}

.menu-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.btn-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
}

.page-list {
  space-y: 4px;
}

.page-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.page-item:hover {
  background: #f5f5f5;
}

.page-item.active {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
}

.page-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #666;
}

.page-title {
  flex: 1;
  font-size: 13px;
  color: #333;
}

.page-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.page-item:hover .page-actions {
  opacity: 1;
}

.action-btn {
  width: 16px;
  height: 16px;
  color: #666;
  cursor: pointer;
  transition: color 0.2s;
}

.action-btn:hover {
  color: #1890ff;
}

.delete-btn:hover {
  color: #ff4d4f;
}

.page-management-list {
  space-y: 8px;
}

.page-management-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  transition: all 0.2s;
}

.page-management-item:hover {
  border-color: #d9d9d9;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.page-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.page-details {
  margin-left: 8px;
}

.page-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.page-path {
  font-size: 12px;
  color: #999;
}

.page-management-item .page-actions {
  opacity: 1;
  gap: 8px;
}
</style>
