<template>
  <div style="padding: 20px;">
    <h1>设计器测试页面</h1>
    <p>如果你能看到这个页面，说明路由配置正确。</p>
    
    <div style="margin: 20px 0;">
      <h2>图标测试</h2>
      <div style="display: flex; gap: 10px; align-items: center;">
        <MdiFileDocument style="font-size: 24px;" />
        <MdiTabUnselected style="font-size: 24px;" />
        <MdiContentSave style="font-size: 24px;" />
        <MdiEye style="font-size: 24px;" />
        <span>图标显示正常</span>
      </div>
    </div>
    
    <div style="margin: 20px 0;">
      <h2>Ant Design 组件测试</h2>
      <Space>
        <Button type="primary">
          <template #icon>
            <MdiContentSave />
          </template>
          保存
        </Button>
        <Button>
          <template #icon>
            <MdiEye />
          </template>
          预览
        </Button>
      </Space>
    </div>
    
    <div style="margin: 20px 0;">
      <h2>布局测试</h2>
      <Layout style="height: 300px; border: 1px solid #d9d9d9;">
        <LayoutSider width="200" theme="light" style="border-right: 1px solid #d9d9d9;">
          <div style="padding: 16px;">左侧边栏</div>
        </LayoutSider>
        <LayoutContent style="padding: 16px;">
          <div>主内容区域</div>
        </LayoutContent>
        <LayoutSider width="200" theme="light" style="border-left: 1px solid #d9d9d9;">
          <div style="padding: 16px;">右侧边栏</div>
        </LayoutSider>
      </Layout>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Layout, Button, Space } from 'ant-design-vue';
import { 
  MdiFileDocument, 
  MdiTabUnselected, 
  MdiContentSave, 
  MdiEye 
} from '@vben/icons';

const { LayoutSider, LayoutContent } = Layout;
</script>
