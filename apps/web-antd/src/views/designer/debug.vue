<template>
  <div class="lowcode-designer">
    <!-- 顶部工具栏 -->
    <LayoutHeader class="designer-header">
      <div class="header-content">
        <div class="header-left">
          <h2 class="designer-title">低代码设计器 - 调试版</h2>
        </div>
        
        <div class="header-center">
          <Space>
            <Button type="primary" @click="handleSave">
              <template #icon>
                <MdiContentSave />
              </template>
              保存
            </Button>
            <Button @click="handlePreview">
              <template #icon>
                <MdiEye />
              </template>
              预览
            </Button>
          </Space>
        </div>
      </div>
    </LayoutHeader>

    <!-- 主体布局 -->
    <Layout class="designer-body">
      <!-- 左侧菜单 -->
      <LayoutSider class="designer-sidebar" :width="320" theme="light">
        <div style="padding: 16px;">
          <h3>侧边栏菜单</h3>
          <div style="margin: 16px 0;">
            <Button 
              :type="activeMainMenu === 'page' ? 'primary' : 'default'"
              @click="activeMainMenu = 'page'"
              style="width: 100%; margin-bottom: 8px;"
            >
              <template #icon>
                <MdiFileDocument />
              </template>
              页面设计
            </Button>
            <Button 
              :type="activeMainMenu === 'tabbar' ? 'primary' : 'default'"
              @click="activeMainMenu = 'tabbar'"
              style="width: 100%;"
            >
              <template #icon>
                <MdiTabUnselected />
              </template>
              TabBar设计
            </Button>
          </div>
        </div>
      </LayoutSider>

      <!-- 中间内容区域 -->
      <LayoutContent class="designer-content">
        <div style="padding: 20px; height: 100%; background: #f5f5f5;">
          <Card title="设计区域" style="height: 100%;">
            <div v-if="activeMainMenu === 'page'">
              <h3>页面设计模式</h3>
              <p>这里将显示页面设计器组件</p>
              <div style="margin: 16px 0;">
                <Button @click="testPageDesigner">测试加载 PageDesigner</Button>
                <span v-if="pageDesignerError" style="color: red; margin-left: 8px;">
                  错误: {{ pageDesignerError }}
                </span>
              </div>
              <!-- 尝试渲染 PageDesigner -->
              <div v-if="showPageDesigner">
                <Suspense>
                  <template #default>
                    <PageDesigner 
                      :selected-component="designerState.selectedComponent"
                      @component-select="handleComponentSelect"
                    />
                  </template>
                  <template #fallback>
                    <div>加载 PageDesigner 中...</div>
                  </template>
                </Suspense>
              </div>
            </div>
            <div v-else>
              <h3>TabBar设计模式</h3>
              <p>这里将显示TabBar设计器组件</p>
              <div style="margin: 16px 0;">
                <Button @click="testTabBarDesigner">测试加载 TabBarDesigner</Button>
                <span v-if="tabBarDesignerError" style="color: red; margin-left: 8px;">
                  错误: {{ tabBarDesignerError }}
                </span>
              </div>
              <!-- 尝试渲染 TabBarDesigner -->
              <div v-if="showTabBarDesigner">
                <Suspense>
                  <template #default>
                    <TabBarDesigner />
                  </template>
                  <template #fallback>
                    <div>加载 TabBarDesigner 中...</div>
                  </template>
                </Suspense>
              </div>
            </div>
          </Card>
        </div>
      </LayoutContent>

      <!-- 右侧属性面板 -->
      <LayoutSider class="designer-property-panel" :width="300" theme="light">
        <div style="padding: 16px;">
          <h3>属性面板</h3>
          <div style="margin: 16px 0;">
            <Button @click="testPropertyPanel">测试加载 PropertyPanel</Button>
            <span v-if="propertyPanelError" style="color: red; margin-left: 8px;">
              错误: {{ propertyPanelError }}
            </span>
          </div>
          <!-- 尝试渲染 PropertyPanel -->
          <div v-if="showPropertyPanel">
            <Suspense>
              <template #default>
                <PropertyPanel :selected-component="designerState.selectedComponent" />
              </template>
              <template #fallback>
                <div>加载 PropertyPanel 中...</div>
              </template>
            </Suspense>
          </div>
        </div>
      </LayoutSider>
    </Layout>
  </div>
</template>

<script setup lang="ts">
import { ref, Suspense, defineAsyncComponent } from 'vue';
import { Layout, Card, Button, Space } from 'ant-design-vue';
import { 
  MdiFileDocument, 
  MdiTabUnselected, 
  MdiContentSave, 
  MdiEye 
} from '@vben/icons';

// 异步导入组件
const PageDesigner = defineAsyncComponent(() => import('./components/PageDesigner.vue'));
const TabBarDesigner = defineAsyncComponent(() => import('./components/TabBarDesigner.vue'));
const PropertyPanel = defineAsyncComponent(() => import('./components/PropertyPanel.vue'));

const { LayoutSider, LayoutContent, LayoutHeader } = Layout;

// 当前激活的主菜单
const activeMainMenu = ref<'page' | 'tabbar'>('page');

// 设计器状态
const designerState = ref({
  currentProject: null,
  selectedComponent: null,
  canUndo: false,
  canRedo: false,
});

// 组件加载状态
const showPageDesigner = ref(false);
const showTabBarDesigner = ref(false);
const showPropertyPanel = ref(false);

// 错误状态
const pageDesignerError = ref('');
const tabBarDesignerError = ref('');
const propertyPanelError = ref('');

// 工具栏操作
const handleSave = () => {
  console.log('保存项目');
};

const handlePreview = () => {
  console.log('预览项目');
};

// 组件选择处理
const handleComponentSelect = (component: any) => {
  designerState.value.selectedComponent = component;
  console.log('选中组件:', component);
};

// 测试组件加载
const testPageDesigner = async () => {
  try {
    pageDesignerError.value = '';
    showPageDesigner.value = true;
    console.log('尝试加载 PageDesigner');
  } catch (error) {
    pageDesignerError.value = String(error);
    console.error('PageDesigner 加载失败:', error);
  }
};

const testTabBarDesigner = async () => {
  try {
    tabBarDesignerError.value = '';
    showTabBarDesigner.value = true;
    console.log('尝试加载 TabBarDesigner');
  } catch (error) {
    tabBarDesignerError.value = String(error);
    console.error('TabBarDesigner 加载失败:', error);
  }
};

const testPropertyPanel = async () => {
  try {
    propertyPanelError.value = '';
    showPropertyPanel.value = true;
    console.log('尝试加载 PropertyPanel');
  } catch (error) {
    propertyPanelError.value = String(error);
    console.error('PropertyPanel 加载失败:', error);
  }
};
</script>

<style scoped>
.lowcode-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.designer-header {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.designer-title {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  color: #262626;
}

.designer-body {
  flex: 1;
  height: calc(100vh - 64px);
}

.designer-sidebar {
  border-right: 1px solid #e8e8e8;
}

.designer-content {
  background: #f5f5f5;
}

.designer-property-panel {
  border-left: 1px solid #e8e8e8;
}
</style>
