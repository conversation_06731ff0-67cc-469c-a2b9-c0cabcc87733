# 低代码PC端拖拽设计器

这是一个专为H5低代码平台设计的PC端可视化设计器，支持拖拽式页面设计和TabBar配置。

## 功能特性

### 🎨 页面设计
- **组件面板**: 提供丰富的基础组件、表单组件、布局组件
- **拖拽画布**: 支持组件拖拽、移动、选择、删除
- **属性编辑**: 实时编辑组件属性和样式
- **页面管理**: 支持多页面创建、切换、删除

### 📱 TabBar设计
- **TabBar配置**: 支持位置、颜色、样式等全面配置
- **项目管理**: 可视化管理TabBar项目和页面绑定
- **实时预览**: 手机模拟器样式的实时预览效果

### 🛠️ 工具栏功能
- **保存**: 保存当前设计项目
- **预览**: 实时预览H5效果
- **导出**: 导出项目配置文件
- **撤销/重做**: 支持操作历史管理

## 技术架构

### 核心组件
```
/src/views/designer/
├── index.vue                    # 设计器主入口
├── components/
│   ├── SidebarMenu.vue         # 侧边栏菜单
│   ├── PageDesigner.vue        # 页面设计模块
│   ├── TabBarDesigner.vue      # TabBar设计模块
│   ├── ComponentPanel.vue      # 组件面板
│   ├── DesignCanvas.vue        # 设计画布
│   └── PropertyPanel.vue       # 属性编辑面板
└── README.md                   # 说明文档
```

### 技术栈
- **Vue 3** + **TypeScript**: 现代化前端框架
- **Ant Design Vue**: UI组件库
- **@vben/icons**: 图标库
- **@vben/lowcode**: 共享组件库

## 使用方法

### 1. 页面设计流程
1. 在左侧菜单选择"页面设计"
2. 从组件面板拖拽组件到画布
3. 点击组件进行选择
4. 在右侧属性面板编辑组件属性
5. 使用顶部工具栏保存或预览

### 2. TabBar设计流程
1. 在左侧菜单选择"TabBar设计"
2. 在"基础配置"标签页设置TabBar样式
3. 在"项目管理"标签页添加和配置TabBar项目
4. 在"预览效果"标签页查看实时效果

### 3. 组件操作
- **拖拽添加**: 从组件面板拖拽到画布
- **点击添加**: 点击组件面板中的组件（自动添加到画布中心）
- **移动组件**: 拖拽画布中的组件进行移动
- **选择组件**: 点击画布中的组件进行选择
- **删除组件**: 选中组件后点击删除按钮

## 设计理念

### 所见即所得
设计器采用"所见即所得"的设计理念，确保PC端设计器中看到的效果与H5端完全一致。

### 组件化架构
- **共享组件库**: 使用`@vben/lowcode`统一管理组件定义
- **模块化设计**: 每个功能模块独立开发和维护
- **类型安全**: 全面使用TypeScript确保类型安全

### 用户体验
- **直观操作**: 拖拽式操作，降低学习成本
- **实时反馈**: 操作即时生效，提供即时反馈
- **响应式布局**: 适配不同屏幕尺寸

## 扩展开发

### 添加新组件
1. 在`@vben/lowcode/components`中定义新组件
2. 在`ComponentPanel.vue`中添加图标映射
3. 在`PropertyPanel.vue`中添加属性配置

### 添加新功能模块
1. 在`components/`目录下创建新的Vue组件
2. 在主入口`index.vue`中引入和使用
3. 在侧边栏菜单中添加对应的菜单项

### 自定义样式
设计器使用CSS变量和模块化样式，可以通过修改对应组件的`<style>`部分来自定义样式。

## 注意事项

1. **组件库依赖**: 确保正确安装和配置`@vben/lowcode`组件库
2. **图标库**: 使用`@vben/icons`提供的图标，保持风格一致
3. **类型定义**: 严格遵循TypeScript类型定义，确保类型安全
4. **性能优化**: 大型项目时注意组件渲染性能优化

## 后续规划

- [ ] 实现撤销/重做功能
- [ ] 添加组件复制/粘贴功能
- [ ] 支持组件层级管理
- [ ] 实现项目模板功能
- [ ] 添加组件动画效果配置
- [ ] 支持自定义组件导入
