<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Button, Space, Divider } from 'ant-design-vue';
import {
  MdiFileDocument,
  MdiTabUnselected,
  MdiContentSave,
  MdiEye,
  MdiExport,
  MdiUndo,
  MdiRedo,
  MdiCog,
} from '@vben/icons';

// 导入低代码核心组件
import { componentRegistry } from '@vben/lowcode/core';
import { AllComponents } from '@vben/lowcode/components';

import SidebarMenu from './components/SidebarMenu.vue';
import PageDesigner from './components/PageDesigner.vue';
import TabBarDesigner from './components/TabBarDesigner.vue';
import PropertyPanel from './components/PropertyPanel.vue';



// 当前激活的主菜单
const activeMainMenu = ref<'page' | 'tabbar'>('page');

// 设计器状态
const designerState = ref({
  currentProject: null,
  selectedComponent: null,
  canUndo: false,
  canRedo: false,
});

// 计算当前显示的组件
const currentComponent = computed(() => {
  return activeMainMenu.value === 'page' ? PageDesigner : TabBarDesigner;
});

// 工具栏操作
const handleSave = async () => {
  try {
    console.log('保存项目');
    // TODO: 实现保存逻辑
    // await saveProject(designerState.value.currentProject);
  } catch (error) {
    console.error('保存失败:', error);
  }
};

const handlePreview = () => {
  console.log('预览项目');
  // TODO: 打开预览窗口
  // window.open('/preview', '_blank');
};

const handleExport = async () => {
  try {
    console.log('导出项目');
    // TODO: 实现导出逻辑
    // const exportData = await exportProject(designerState.value.currentProject);
    // downloadFile(exportData, 'project.json');
  } catch (error) {
    console.error('导出失败:', error);
  }
};

const handleUndo = () => {
  if (designerState.value.canUndo) {
    console.log('撤销操作');
    // TODO: 实现撤销逻辑
  }
};

const handleRedo = () => {
  if (designerState.value.canRedo) {
    console.log('重做操作');
    // TODO: 实现重做逻辑
  }
};

const handleSettings = () => {
  console.log('打开设置');
  // TODO: 打开设置面板
};

// 菜单切换处理
const handleMenuChange = (menu: 'page' | 'tabbar') => {
  activeMainMenu.value = menu;
};

// 组件选择处理
const handleComponentSelect = (component: any) => {
  designerState.value.selectedComponent = component;
};

onMounted(() => {
  // 初始化设计器
  console.log('设计器初始化');

  // 注册所有低代码组件
  try {
    console.log('开始注册组件，总数:', AllComponents.length);
    AllComponents.forEach((component, index) => {
      if (component && component.type) {
        componentRegistry.register(component);
        console.log(`[${index + 1}] 已注册组件: ${component.type} - ${component.label}`);
      } else {
        console.warn(`组件 ${index} 无效:`, component);
      }
    });

    // 验证注册结果
    const registeredComponents = componentRegistry.getAllComponentDefinitions();
    console.log(`✅ 成功注册了 ${registeredComponents.length} 个组件`);
    console.log('已注册的组件类型:', registeredComponents.map(c => c.type));

    // 验证分类
    const categories = componentRegistry.getAllCategories();
    console.log('可用分类:', categories);

    // 测试创建一个按钮组件
    setTimeout(() => {
      const buttonDef = componentRegistry.getComponentDefinition('button');
      if (buttonDef) {
        console.log('✅ 找到按钮组件定义:', buttonDef);
      } else {
        console.error('❌ 未找到按钮组件定义');
      }
    }, 1000);

  } catch (error) {
    console.error('❌ 注册组件时出错:', error);
  }
});
</script>

<template>
  <div class="h-screen flex flex-col bg-gray-50">
    <!-- 顶部工具栏 -->
    <div class="h-16 px-6 bg-white border-b border-gray-200 flex items-center justify-between shadow-sm">
      <div class="flex items-center">
        <h2 class="text-xl font-semibold text-gray-900">低代码设计器</h2>
        <span class="ml-4 px-2 py-1 text-xs text-gray-600 bg-gray-100 rounded-full">
          已注册 {{ Object.keys(componentRegistry.getAllComponentDefinitions()).length }} 个组件
        </span>
      </div>

      <div class="flex items-center">
        <Space>
          <Button
            type="primary"
            @click="handleSave"
            class="shadow-sm"
          >
            <template #icon>
              <MdiContentSave />
            </template>
            保存
          </Button>
          <Button
            @click="handlePreview"
            class="border-gray-300"
          >
            <template #icon>
              <MdiEye />
            </template>
            预览
          </Button>
          <Button
            @click="handleExport"
            class="border-gray-300"
          >
            <template #icon>
              <MdiExport />
            </template>
            导出
          </Button>
          <Divider type="vertical" />
          <Button
            :disabled="!designerState.canUndo"
            @click="handleUndo"
            class="border-gray-300"
          >
            <template #icon>
              <MdiUndo />
            </template>
            撤销
          </Button>
          <Button
            :disabled="!designerState.canRedo"
            @click="handleRedo"
            class="border-gray-300"
          >
            <template #icon>
              <MdiRedo />
            </template>
            重做
          </Button>
        </Space>
      </div>

      <div class="flex items-center">
        <Button
          type="text"
          @click="handleSettings"
          class="text-gray-600 hover:text-gray-900"
        >
          <template #icon>
            <MdiCog />
          </template>
          设置
        </Button>
      </div>
    </div>

    <!-- 主体布局 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 左侧菜单 -->
      <div class="w-80 bg-white border-r border-gray-200 flex-shrink-0 shadow-sm">
        <SidebarMenu
          :active-menu="activeMainMenu"
          @menu-change="handleMenuChange"
        />
      </div>

      <!-- 中间内容区域 -->
      <div class="flex-1 min-w-0">
        <component
          :is="currentComponent"
          :selected-component="designerState.selectedComponent"
          @component-select="handleComponentSelect"
        />
      </div>

      <!-- 右侧属性面板 -->
      <div class="w-80 bg-white border-l border-gray-200 flex-shrink-0 shadow-sm">
        <PropertyPanel
          :selected-component="designerState.selectedComponent"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
</style>
