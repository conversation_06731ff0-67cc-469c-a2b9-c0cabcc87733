<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Layout,  LayoutSider, LayoutContent, LayoutHeader, Card, Button, Space, Divider } from 'ant-design-vue';
import {
  MdiFileDocument,
  MdiTabUnselected,
  MdiContentSave,
  MdiEye,
  MdiExport,
  MdiUndo,
  MdiRedo,
  MdiCog,
} from '@vben/icons';

// 导入低代码核心组件
import { componentRegistry } from '@vben/lowcode/core';
import { AllComponents } from '@vben/lowcode/components';

import SidebarMenu from './components/SidebarMenu.vue';
import PageDesigner from './components/PageDesigner.vue';
import TabBarDesigner from './components/TabBarDesigner.vue';
import PropertyPanel from './components/PropertyPanel.vue';

// const { LayoutSider, LayoutContent, LayoutHeader } = Layout;

// 当前激活的主菜单
const activeMainMenu = ref<'page' | 'tabbar'>('page');

// 设计器状态
const designerState = ref({
  currentProject: null,
  selectedComponent: null,
  canUndo: false,
  canRedo: false,
});

// 计算当前显示的组件
const currentComponent = computed(() => {
  return activeMainMenu.value === 'page' ? PageDesigner : TabBarDesigner;
});

// 工具栏操作
const handleSave = async () => {
  try {
    console.log('保存项目');
    // TODO: 实现保存逻辑
    // await saveProject(designerState.value.currentProject);
  } catch (error) {
    console.error('保存失败:', error);
  }
};

const handlePreview = () => {
  console.log('预览项目');
  // TODO: 打开预览窗口
  // window.open('/preview', '_blank');
};

const handleExport = async () => {
  try {
    console.log('导出项目');
    // TODO: 实现导出逻辑
    // const exportData = await exportProject(designerState.value.currentProject);
    // downloadFile(exportData, 'project.json');
  } catch (error) {
    console.error('导出失败:', error);
  }
};

const handleUndo = () => {
  if (designerState.value.canUndo) {
    console.log('撤销操作');
    // TODO: 实现撤销逻辑
  }
};

const handleRedo = () => {
  if (designerState.value.canRedo) {
    console.log('重做操作');
    // TODO: 实现重做逻辑
  }
};

const handleSettings = () => {
  console.log('打开设置');
  // TODO: 打开设置面板
};

// 菜单切换处理
const handleMenuChange = (menu: 'page' | 'tabbar') => {
  activeMainMenu.value = menu;
};

// 组件选择处理
const handleComponentSelect = (component: any) => {
  designerState.value.selectedComponent = component;
};

onMounted(() => {
  // 初始化设计器
  console.log('设计器初始化');

  // 注册所有低代码组件
  try {
    AllComponents.forEach(component => {
      if (component && component.type) {
        componentRegistry.register(component);
        console.log(`已注册组件: ${component.type} - ${component.label}`);
      }
    });
    console.log(`总共注册了 ${AllComponents.length} 个组件`);
  } catch (error) {
    console.error('注册组件时出错:', error);
  }
});
</script>

<template>
  <div class="lowcode-designer">
    <!-- 顶部工具栏 -->
    <LayoutHeader class="designer-header">
      <div class="header-content">
        <div class="header-left">
          <h2 class="designer-title">低代码设计器</h2>
        </div>
        
        <div class="header-center">
          <Space>
            <Button
              type="primary"
              @click="handleSave"
            >
              <template #icon>
                <MdiContentSave />
              </template>
              保存
            </Button>
            <Button
              @click="handlePreview"
            >
              <template #icon>
                <MdiEye />
              </template>
              预览
            </Button>
            <Button
              @click="handleExport"
            >
              <template #icon>
                <MdiExport />
              </template>
              导出
            </Button>
            <Divider type="vertical" />
            <Button
              :disabled="!designerState.canUndo"
              @click="handleUndo"
            >
              <template #icon>
                <MdiUndo />
              </template>
              撤销
            </Button>
            <Button
              :disabled="!designerState.canRedo"
              @click="handleRedo"
            >
              <template #icon>
                <MdiRedo />
              </template>
              重做
            </Button>
          </Space>
        </div>
        
        <div class="header-right">
          <Button
            type="text"
            @click="handleSettings"
          >
            <template #icon>
              <MdiCog />
            </template>
            设置
          </Button>
        </div>
      </div>
    </LayoutHeader>

    <!-- 主体布局 -->
    <Layout class="designer-body">
      <!-- 左侧菜单 -->
      <LayoutSider 
        class="designer-sidebar"
        :width="320"
        theme="light"
      >
        <SidebarMenu 
          :active-menu="activeMainMenu"
          @menu-change="handleMenuChange"
        />
      </LayoutSider>

      <!-- 中间内容区域 -->
      <LayoutContent class="designer-content">
        <component 
          :is="currentComponent"
          :selected-component="designerState.selectedComponent"
          @component-select="handleComponentSelect"
        />
      </LayoutContent>

      <!-- 右侧属性面板 -->
      <LayoutSider 
        class="designer-property-panel"
        :width="300"
        theme="light"
        position="right"
      >
        <PropertyPanel 
          :selected-component="designerState.selectedComponent"
        />
      </LayoutSider>
    </Layout>
  </div>
</template>

<style scoped>
/* 主容器 - 现代化深色主题 */
.lowcode-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  overflow: hidden;
}

/* 顶部工具栏 - 玻璃态效果 */
.designer-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: none;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0 24px;
  height: 64px;
  line-height: 64px;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  max-width: 1920px;
  margin: 0 auto;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.designer-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
}

.header-center {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 工具栏按钮优化 */
.header-center :deep(.ant-btn) {
  height: 40px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-center :deep(.ant-btn-primary) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.header-center :deep(.ant-btn-primary:hover) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
}

.header-center :deep(.ant-btn:not(.ant-btn-primary)) {
  background: rgba(255, 255, 255, 0.9);
  color: #4a5568;
}

.header-center :deep(.ant-btn:not(.ant-btn-primary):hover) {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.header-right :deep(.ant-btn) {
  height: 40px;
  width: 40px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: #4a5568;
  transition: all 0.3s ease;
}

.header-right :deep(.ant-btn:hover) {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* 主体布局 */
.designer-body {
  flex: 1;
  height: calc(100vh - 64px);
  display: flex;
  background: #f8fafc;
  position: relative;
}

/* 左侧边栏 - 现代卡片设计 */
.designer-sidebar {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: none;
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 10;
  overflow: hidden;
}

.designer-sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

/* 中间内容区域 - 设计画布 */
.designer-content {
  background: #f8fafc;
  padding: 0;
  position: relative;
  flex: 1;
  overflow: hidden;
}

.designer-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* 右侧属性面板 */
.designer-property-panel {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: none;
  border-left: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: -2px 0 20px rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 10;
  overflow: hidden;
}

.designer-property-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #764ba2 0%, #667eea 100%);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .designer-sidebar {
    width: 280px !important;
  }

  .designer-property-panel {
    width: 260px !important;
  }
}

@media (max-width: 768px) {
  .designer-header {
    padding: 0 16px;
  }

  .header-center {
    gap: 4px;
  }

  .header-center :deep(.ant-btn) {
    height: 36px;
    font-size: 12px;
    padding: 0 12px;
  }

  .designer-title {
    font-size: 18px;
  }

  .designer-sidebar {
    width: 240px !important;
  }

  .designer-property-panel {
    width: 240px !important;
  }
}

/* 滚动条美化 */
:deep(.ant-layout-sider-children) {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

:deep(.ant-layout-sider-children::-webkit-scrollbar) {
  width: 6px;
}

:deep(.ant-layout-sider-children::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.ant-layout-sider-children::-webkit-scrollbar-thumb) {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

:deep(.ant-layout-sider-children::-webkit-scrollbar-thumb:hover) {
  background: rgba(0, 0, 0, 0.3);
}

/* 动画效果 */
.designer-sidebar,
.designer-property-panel {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.lowcode-designer > * {
  animation: fadeInUp 0.6s ease-out;
}

.designer-header {
  animation-delay: 0.1s;
}

.designer-body {
  animation-delay: 0.2s;
}
</style>
