<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Layout, Card, Button, Space, Divider } from 'ant-design-vue';
import { 
  MdiFileDocument, 
  MdiTabUnselected, 
  MdiContentSave, 
  MdiEye, 
  MdiExport,
  MdiUndo,
  MdiRedo,
  MdiCog
} from '@vben/icons';

import SidebarMenu from './components/SidebarMenu.vue';
import PageDesigner from './components/PageDesigner.vue';
import TabBarDesigner from './components/TabBarDesigner.vue';
import PropertyPanel from './components/PropertyPanel.vue';

const { LayoutSider, LayoutContent, LayoutHeader } = Layout;

// 当前激活的主菜单
const activeMainMenu = ref<'page' | 'tabbar'>('page');

// 设计器状态
const designerState = ref({
  currentProject: null,
  selectedComponent: null,
  canUndo: false,
  canRedo: false,
});

// 计算当前显示的组件
const currentComponent = computed(() => {
  return activeMainMenu.value === 'page' ? PageDesigner : TabBarDesigner;
});

// 工具栏操作
const handleSave = async () => {
  try {
    console.log('保存项目');
    // TODO: 实现保存逻辑
    // await saveProject(designerState.value.currentProject);
  } catch (error) {
    console.error('保存失败:', error);
  }
};

const handlePreview = () => {
  console.log('预览项目');
  // TODO: 打开预览窗口
  // window.open('/preview', '_blank');
};

const handleExport = async () => {
  try {
    console.log('导出项目');
    // TODO: 实现导出逻辑
    // const exportData = await exportProject(designerState.value.currentProject);
    // downloadFile(exportData, 'project.json');
  } catch (error) {
    console.error('导出失败:', error);
  }
};

const handleUndo = () => {
  if (designerState.value.canUndo) {
    console.log('撤销操作');
    // TODO: 实现撤销逻辑
  }
};

const handleRedo = () => {
  if (designerState.value.canRedo) {
    console.log('重做操作');
    // TODO: 实现重做逻辑
  }
};

const handleSettings = () => {
  console.log('打开设置');
  // TODO: 打开设置面板
};

// 菜单切换处理
const handleMenuChange = (menu: 'page' | 'tabbar') => {
  activeMainMenu.value = menu;
};

// 组件选择处理
const handleComponentSelect = (component: any) => {
  designerState.value.selectedComponent = component;
};

onMounted(() => {
  // 初始化设计器
  console.log('设计器初始化');
});
</script>

<template>
  <div class="lowcode-designer">
    <!-- 顶部工具栏 -->
    <LayoutHeader class="designer-header">
      <div class="header-content">
        <div class="header-left">
          <h2 class="designer-title">低代码设计器</h2>
        </div>
        
        <div class="header-center">
          <Space>
            <Button
              type="primary"
              @click="handleSave"
            >
              <template #icon>
                <MdiContentSave />
              </template>
              保存
            </Button>
            <Button
              @click="handlePreview"
            >
              <template #icon>
                <MdiEye />
              </template>
              预览
            </Button>
            <Button
              @click="handleExport"
            >
              <template #icon>
                <MdiExport />
              </template>
              导出
            </Button>
            <Divider type="vertical" />
            <Button
              :disabled="!designerState.canUndo"
              @click="handleUndo"
            >
              <template #icon>
                <MdiUndo />
              </template>
              撤销
            </Button>
            <Button
              :disabled="!designerState.canRedo"
              @click="handleRedo"
            >
              <template #icon>
                <MdiRedo />
              </template>
              重做
            </Button>
          </Space>
        </div>
        
        <div class="header-right">
          <Button
            type="text"
            @click="handleSettings"
          >
            <template #icon>
              <MdiCog />
            </template>
            设置
          </Button>
        </div>
      </div>
    </LayoutHeader>

    <!-- 主体布局 -->
    <Layout class="designer-body">
      <!-- 左侧菜单 -->
      <LayoutSider 
        class="designer-sidebar"
        :width="320"
        theme="light"
      >
        <SidebarMenu 
          :active-menu="activeMainMenu"
          @menu-change="handleMenuChange"
        />
      </LayoutSider>

      <!-- 中间内容区域 -->
      <LayoutContent class="designer-content">
        <component 
          :is="currentComponent"
          :selected-component="designerState.selectedComponent"
          @component-select="handleComponentSelect"
        />
      </LayoutContent>

      <!-- 右侧属性面板 -->
      <LayoutSider 
        class="designer-property-panel"
        :width="300"
        theme="light"
        position="right"
      >
        <PropertyPanel 
          :selected-component="designerState.selectedComponent"
        />
      </LayoutSider>
    </Layout>
  </div>
</template>

<style scoped>
.lowcode-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.designer-header {
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
  padding: 0 16px;
  height: 56px;
  line-height: 56px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.designer-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.designer-body {
  flex: 1;
  height: calc(100vh - 56px);
}

.designer-sidebar {
  border-right: 1px solid #e8e8e8;
  background: #fff;
}

.designer-content {
  background: #f5f5f5;
  padding: 0;
}

.designer-property-panel {
  border-left: 1px solid #e8e8e8;
  background: #fff;
}
</style>
