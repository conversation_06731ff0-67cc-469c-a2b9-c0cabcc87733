<script setup lang="ts">
import { ref, computed, watch, h } from 'vue';
import { 
  Card, 
  Form, 
  FormItem, 
  Input, 
  InputNumber, 
  Select, 
  Switch, 
  ColorPicker, 
  Tabs, 
  Empty,
  Divider
} from 'ant-design-vue';
import { MdiCog, MdiPalette, MdiLink } from '@vben/icons';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

interface Props {
  selectedComponent?: any;
}

interface Emits {
  (e: 'property-change', componentId: string, property: string, value: any): void;
  (e: 'style-change', componentId: string, style: Record<string, any>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 当前激活的标签页
const activeTab = ref('properties');

// 组件属性表单数据
const componentProps = ref<Record<string, any>>({});

// 组件样式表单数据
const componentStyles = ref<Record<string, any>>({});

// 监听选中组件变化
watch(
  () => props.selectedComponent,
  (newComponent) => {
    if (newComponent) {
      componentProps.value = { ...newComponent.props };
      componentStyles.value = { ...newComponent.style };
    } else {
      componentProps.value = {};
      componentStyles.value = {};
    }
  },
  { immediate: true }
);

// 获取组件属性配置
const getComponentPropertyConfig = (componentType: string) => {
  const configs: Record<string, any[]> = {
    button: [
      { key: 'text', label: '按钮文字', type: 'input', defaultValue: '按钮' },
      { key: 'type', label: '按钮类型', type: 'select', options: [
        { label: '默认', value: 'default' },
        { label: '主要', value: 'primary' },
        { label: '成功', value: 'success' },
        { label: '警告', value: 'warning' },
        { label: '危险', value: 'danger' },
      ], defaultValue: 'default' },
      { key: 'disabled', label: '禁用状态', type: 'switch', defaultValue: false },
      { key: 'loading', label: '加载状态', type: 'switch', defaultValue: false },
    ],
    text: [
      { key: 'content', label: '文本内容', type: 'textarea', defaultValue: '文本内容' },
      { key: 'fontSize', label: '字体大小', type: 'number', defaultValue: 14, unit: 'px' },
      { key: 'fontWeight', label: '字体粗细', type: 'select', options: [
        { label: '正常', value: 'normal' },
        { label: '粗体', value: 'bold' },
        { label: '细体', value: 'lighter' },
      ], defaultValue: 'normal' },
      { key: 'textAlign', label: '文本对齐', type: 'select', options: [
        { label: '左对齐', value: 'left' },
        { label: '居中', value: 'center' },
        { label: '右对齐', value: 'right' },
      ], defaultValue: 'left' },
    ],
    input: [
      { key: 'placeholder', label: '占位符', type: 'input', defaultValue: '请输入内容' },
      { key: 'disabled', label: '禁用状态', type: 'switch', defaultValue: false },
      { key: 'readonly', label: '只读状态', type: 'switch', defaultValue: false },
      { key: 'maxLength', label: '最大长度', type: 'number', defaultValue: null },
    ],
    image: [
      { key: 'src', label: '图片地址', type: 'input', defaultValue: '' },
      { key: 'alt', label: '替代文字', type: 'input', defaultValue: '' },
      { key: 'fit', label: '填充模式', type: 'select', options: [
        { label: '填充', value: 'fill' },
        { label: '包含', value: 'contain' },
        { label: '覆盖', value: 'cover' },
        { label: '缩放', value: 'scale-down' },
      ], defaultValue: 'fill' },
    ],
  };
  
  return configs[componentType] || [];
};

// 获取样式配置
const getStyleConfig = () => [
  { key: 'width', label: '宽度', type: 'input', unit: 'px' },
  { key: 'height', label: '高度', type: 'input', unit: 'px' },
  { key: 'backgroundColor', label: '背景颜色', type: 'color' },
  { key: 'color', label: '文字颜色', type: 'color' },
  { key: 'borderRadius', label: '圆角', type: 'number', unit: 'px' },
  { key: 'padding', label: '内边距', type: 'input', unit: 'px' },
  { key: 'margin', label: '外边距', type: 'input', unit: 'px' },
  { key: 'border', label: '边框', type: 'input' },
];

// 处理属性变化
const handlePropertyChange = (key: string, value: any) => {
  if (!props.selectedComponent) return;
  
  componentProps.value[key] = value;
  emit('property-change', props.selectedComponent.id, key, value);
};

// 处理样式变化
const handleStyleChange = (key: string, value: any) => {
  if (!props.selectedComponent) return;
  
  componentStyles.value[key] = value;
  emit('style-change', props.selectedComponent.id, { [key]: value });
};

// 渲染表单项
const renderFormItem = (config: any, value: any, onChange: (value: any) => void) => {
  switch (config.type) {
    case 'input':
      return h(Input, {
        value,
        placeholder: config.placeholder || `请输入${config.label}`,
        'onUpdate:value': onChange,
      });
    
    case 'textarea':
      return h(TextArea, {
        value,
        placeholder: config.placeholder || `请输入${config.label}`,
        rows: 3,
        'onUpdate:value': onChange,
      });
    
    case 'number':
      return h(InputNumber, {
        value,
        min: config.min,
        max: config.max,
        'onUpdate:value': onChange,
      });
    
    case 'select':
      return h(Select, {
        value,
        placeholder: `请选择${config.label}`,
        'onUpdate:value': onChange,
      }, () => config.options?.map((option: any) => 
        h(Option, { key: option.value, value: option.value }, () => option.label)
      ));
    
    case 'switch':
      return h(Switch, {
        checked: value,
        'onUpdate:checked': onChange,
      });
    
    case 'color':
      return h(ColorPicker, {
        value,
        'onUpdate:value': onChange,
      });
    
    default:
      return h(Input, {
        value,
        'onUpdate:value': onChange,
      });
  }
};

// 计算当前组件的属性配置
const currentPropertyConfig = computed(() => {
  if (!props.selectedComponent) return [];
  return getComponentPropertyConfig(props.selectedComponent.type);
});

// 计算样式配置
const styleConfig = computed(() => getStyleConfig());
</script>

<template>
  <div class="property-panel">
    <div class="panel-header">
      <h3>属性面板</h3>
    </div>

    <div class="panel-content">
      <!-- 未选中组件时的提示 -->
      <div v-if="!selectedComponent" class="empty-state">
        <Empty 
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
          description="请选择一个组件"
        >
          <template #description>
            <span class="empty-description">
              在画布中点击组件<br>即可编辑其属性
            </span>
          </template>
        </Empty>
      </div>

      <!-- 选中组件时的属性编辑 -->
      <div v-else class="property-editor">
        <div class="component-info">
          <div class="component-type">{{ selectedComponent.type }}</div>
          <div class="component-id">ID: {{ selectedComponent.id }}</div>
        </div>

        <Tabs v-model:activeKey="activeTab" size="small">
          <!-- 属性配置 -->
          <TabPane key="properties" tab="属性">
            <template #tab>
              <span>
                <MdiCog style="margin-right: 4px;" />
                属性
              </span>
            </template>

            <div class="properties-form">
              <Form layout="vertical" size="small">
                <FormItem
                  v-for="config in currentPropertyConfig"
                  :key="config.key"
                  :label="config.label"
                >
                  <div class="form-item-wrapper">
                    <component
                      :is="renderFormItem(
                        config,
                        componentProps[config.key] ?? config.defaultValue,
                        (value) => handlePropertyChange(config.key, value)
                      )"
                    />
                    <span v-if="config.unit" class="unit-label">{{ config.unit }}</span>
                  </div>
                </FormItem>

                <div v-if="currentPropertyConfig.length === 0" class="no-properties">
                  <span class="no-properties-text">该组件暂无可配置属性</span>
                </div>
              </Form>
            </div>
          </TabPane>

          <!-- 样式配置 -->
          <TabPane key="styles" tab="样式">
            <template #tab>
              <span>
                <MdiPalette style="margin-right: 4px;" />
                样式
              </span>
            </template>

            <div class="styles-form">
              <Form layout="vertical" size="small">
                <FormItem
                  v-for="config in styleConfig"
                  :key="config.key"
                  :label="config.label"
                >
                  <div class="form-item-wrapper">
                    <component
                      :is="renderFormItem(
                        config,
                        componentStyles[config.key],
                        (value) => handleStyleChange(config.key, value)
                      )"
                    />
                    <span v-if="config.unit" class="unit-label">{{ config.unit }}</span>
                  </div>
                </FormItem>
              </Form>
            </div>
          </TabPane>

          <!-- 事件配置 -->
          <TabPane key="events" tab="事件">
            <template #tab>
              <span>
                <MdiLink style="margin-right: 4px;" />
                事件
              </span>
            </template>

            <div class="events-form">
              <div class="coming-soon">
                <span>事件配置功能即将上线</span>
              </div>
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
  </div>
</template>

<style scoped>
.property-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.panel-content {
  flex: 1;
  overflow: hidden;
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.empty-description {
  color: #8c8c8c;
  font-size: 13px;
  line-height: 1.5;
  text-align: center;
}

.property-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.component-info {
  padding: 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.component-type {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.component-id {
  font-size: 12px;
  color: #8c8c8c;
}

.properties-form,
.styles-form,
.events-form {
  padding: 16px;
  height: calc(100vh - 280px);
  overflow-y: auto;
}

.form-item-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-item-wrapper > * {
  flex: 1;
}

.unit-label {
  flex: none;
  font-size: 12px;
  color: #8c8c8c;
  min-width: 20px;
}

.no-properties {
  text-align: center;
  padding: 40px 20px;
}

.no-properties-text {
  color: #8c8c8c;
  font-size: 13px;
}

.coming-soon {
  text-align: center;
  padding: 40px 20px;
  color: #8c8c8c;
  font-size: 13px;
}

/* 滚动条样式 */
.properties-form::-webkit-scrollbar,
.styles-form::-webkit-scrollbar,
.events-form::-webkit-scrollbar {
  width: 4px;
}

.properties-form::-webkit-scrollbar-track,
.styles-form::-webkit-scrollbar-track,
.events-form::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 2px;
}

.properties-form::-webkit-scrollbar-thumb,
.styles-form::-webkit-scrollbar-thumb,
.events-form::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}

.properties-form::-webkit-scrollbar-thumb:hover,
.styles-form::-webkit-scrollbar-thumb:hover,
.events-form::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
</style>
