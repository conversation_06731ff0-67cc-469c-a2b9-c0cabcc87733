<script setup lang="ts">
import { ref, computed, watch, h } from 'vue';
import {
  Card,
  Form,
  FormItem,
  Input,
  InputNumber,
  Select,
  Switch,
  Tabs,
  Empty,
  Divider
} from 'ant-design-vue';
import { <PERSON>diCog, MdiPalette, MdiLink } from '@vben/icons';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

interface Props {
  selectedComponent?: any;
}

interface Emits {
  (e: 'property-change', componentId: string, property: string, value: any): void;
  (e: 'style-change', componentId: string, style: Record<string, any>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 当前激活的标签页
const activeTab = ref('properties');

// 组件属性表单数据
const componentProps = ref<Record<string, any>>({});

// 组件样式表单数据
const componentStyles = ref<Record<string, any>>({});

// 监听选中组件变化
watch(
  () => props.selectedComponent,
  (newComponent) => {
    if (newComponent) {
      componentProps.value = { ...newComponent.props };
      componentStyles.value = { ...newComponent.style };
    } else {
      componentProps.value = {};
      componentStyles.value = {};
    }
  },
  { immediate: true }
);

// 获取组件属性配置
const getComponentPropertyConfig = (componentType: string) => {
  const configs: Record<string, any[]> = {
    button: [
      { key: 'text', label: '按钮文字', type: 'input', defaultValue: '按钮' },
      { key: 'type', label: '按钮类型', type: 'select', options: [
        { label: '默认', value: 'default' },
        { label: '主要', value: 'primary' },
        { label: '成功', value: 'success' },
        { label: '警告', value: 'warning' },
        { label: '危险', value: 'danger' },
      ], defaultValue: 'default' },
      { key: 'disabled', label: '禁用状态', type: 'switch', defaultValue: false },
      { key: 'loading', label: '加载状态', type: 'switch', defaultValue: false },
    ],
    text: [
      { key: 'content', label: '文本内容', type: 'textarea', defaultValue: '文本内容' },
      { key: 'fontSize', label: '字体大小', type: 'number', defaultValue: 14, unit: 'px' },
      { key: 'fontWeight', label: '字体粗细', type: 'select', options: [
        { label: '正常', value: 'normal' },
        { label: '粗体', value: 'bold' },
        { label: '细体', value: 'lighter' },
      ], defaultValue: 'normal' },
      { key: 'textAlign', label: '文本对齐', type: 'select', options: [
        { label: '左对齐', value: 'left' },
        { label: '居中', value: 'center' },
        { label: '右对齐', value: 'right' },
      ], defaultValue: 'left' },
    ],
    input: [
      { key: 'placeholder', label: '占位符', type: 'input', defaultValue: '请输入内容' },
      { key: 'disabled', label: '禁用状态', type: 'switch', defaultValue: false },
      { key: 'readonly', label: '只读状态', type: 'switch', defaultValue: false },
      { key: 'maxLength', label: '最大长度', type: 'number', defaultValue: null },
    ],
    image: [
      { key: 'src', label: '图片地址', type: 'input', defaultValue: '' },
      { key: 'alt', label: '替代文字', type: 'input', defaultValue: '' },
      { key: 'fit', label: '填充模式', type: 'select', options: [
        { label: '填充', value: 'fill' },
        { label: '包含', value: 'contain' },
        { label: '覆盖', value: 'cover' },
        { label: '缩放', value: 'scale-down' },
      ], defaultValue: 'fill' },
    ],
  };
  
  return configs[componentType] || [];
};

// 获取样式配置
const getStyleConfig = () => [
  { key: 'width', label: '宽度', type: 'input', unit: 'px' },
  { key: 'height', label: '高度', type: 'input', unit: 'px' },
  { key: 'backgroundColor', label: '背景颜色', type: 'color' },
  { key: 'color', label: '文字颜色', type: 'color' },
  { key: 'borderRadius', label: '圆角', type: 'number', unit: 'px' },
  { key: 'padding', label: '内边距', type: 'input', unit: 'px' },
  { key: 'margin', label: '外边距', type: 'input', unit: 'px' },
  { key: 'border', label: '边框', type: 'input' },
];

// 处理属性变化
const handlePropertyChange = (key: string, value: any) => {
  if (!props.selectedComponent) return;
  
  componentProps.value[key] = value;
  emit('property-change', props.selectedComponent.id, key, value);
};

// 处理样式变化
const handleStyleChange = (key: string, value: any) => {
  if (!props.selectedComponent) return;
  
  componentStyles.value[key] = value;
  emit('style-change', props.selectedComponent.id, { [key]: value });
};

// 渲染表单项
const renderFormItem = (config: any, value: any, onChange: (value: any) => void) => {
  switch (config.type) {
    case 'input':
      return h(Input, {
        value,
        placeholder: config.placeholder || `请输入${config.label}`,
        'onUpdate:value': onChange,
      });
    
    case 'textarea':
      return h(TextArea, {
        value,
        placeholder: config.placeholder || `请输入${config.label}`,
        rows: 3,
        'onUpdate:value': onChange,
      });
    
    case 'number':
      return h(InputNumber, {
        value,
        min: config.min,
        max: config.max,
        'onUpdate:value': onChange,
      });
    
    case 'select':
      return h(Select, {
        value,
        placeholder: `请选择${config.label}`,
        'onUpdate:value': onChange,
      }, () => config.options?.map((option: any) => 
        h(Option, { key: option.value, value: option.value }, () => option.label)
      ));
    
    case 'switch':
      return h(Switch, {
        checked: value,
        'onUpdate:checked': onChange,
      });
    
    case 'color':
      return h(Input, {
        value,
        'onUpdate:value': onChange,
        placeholder: '#ffffff',
        style: { width: '100%' },
      });
    
    default:
      return h(Input, {
        value,
        'onUpdate:value': onChange,
      });
  }
};

// 计算当前组件的属性配置
const currentPropertyConfig = computed(() => {
  if (!props.selectedComponent) return [];
  return getComponentPropertyConfig(props.selectedComponent.type);
});

// 计算样式配置
const styleConfig = computed(() => getStyleConfig());
</script>

<template>
  <div class="h-full flex flex-col bg-white">
    <div class="p-4 border-b border-gray-200 bg-gray-50">
      <h3 class="text-lg font-semibold text-gray-900">属性面板</h3>
    </div>

    <div class="flex-1 overflow-auto">
      <!-- 未选中组件时的提示 -->
      <div v-if="!selectedComponent" class="flex items-center justify-center h-full">
        <Empty
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
          description="请选择一个组件"
        >
          <template #description>
            <div class="text-gray-500 text-center">
              <div class="text-sm">在画布中点击组件</div>
              <div class="text-sm">即可编辑其属性</div>
            </div>
          </template>
        </Empty>
      </div>

      <!-- 选中组件时的属性编辑 -->
      <div v-else class="p-4">
        <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-sm font-semibold text-blue-900">{{ selectedComponent.type }}</div>
              <div class="text-xs text-blue-600 mt-1">ID: {{ selectedComponent.id }}</div>
            </div>
            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
              <span class="text-blue-600 text-xs">🎯</span>
            </div>
          </div>
        </div>

        <Tabs v-model:activeKey="activeTab" size="small" class="property-tabs">
          <!-- 属性配置 -->
          <TabPane key="properties" tab="属性">
            <template #tab>
              <span class="flex items-center">
                <MdiCog class="mr-1 w-4 h-4" />
                属性
              </span>
            </template>

            <div class="mt-4">
              <Form layout="vertical" size="small">
                <FormItem
                  v-for="config in currentPropertyConfig"
                  :key="config.key"
                  :label="config.label"
                  class="mb-4"
                >
                  <div class="flex items-center">
                    <component
                      :is="renderFormItem(
                        config,
                        componentProps[config.key] ?? config.defaultValue,
                        (value) => handlePropertyChange(config.key, value)
                      )"
                      class="flex-1"
                    />
                    <span v-if="config.unit" class="ml-2 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{{ config.unit }}</span>
                  </div>
                </FormItem>

                <div v-if="currentPropertyConfig.length === 0" class="text-center py-12">
                  <div class="text-gray-400 text-2xl mb-2">⚙️</div>
                  <div class="text-sm text-gray-500">该组件暂无可配置属性</div>
                </div>
              </Form>
            </div>
          </TabPane>

          <!-- 样式配置 -->
          <TabPane key="styles" tab="样式">
            <template #tab>
              <span class="flex items-center">
                <MdiPalette class="mr-1 w-4 h-4" />
                样式
              </span>
            </template>

            <div class="mt-4">
              <Form layout="vertical" size="small">
                <FormItem
                  v-for="config in styleConfig"
                  :key="config.key"
                  :label="config.label"
                  class="mb-4"
                >
                  <div class="flex items-center">
                    <component
                      :is="renderFormItem(
                        config,
                        componentStyles[config.key],
                        (value) => handleStyleChange(config.key, value)
                      )"
                      class="flex-1"
                    />
                    <span v-if="config.unit" class="ml-2 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{{ config.unit }}</span>
                  </div>
                </FormItem>

                <div v-if="styleConfig.length === 0" class="text-center py-12">
                  <div class="text-gray-400 text-2xl mb-2">🎨</div>
                  <div class="text-sm text-gray-500">该组件暂无可配置样式</div>
                </div>
              </Form>
            </div>
          </TabPane>

          <!-- 事件配置 -->
          <TabPane key="events" tab="事件">
            <template #tab>
              <span class="flex items-center">
                <MdiLink class="mr-1 w-4 h-4" />
                事件
              </span>
            </template>

            <div class="mt-4">
              <div class="text-center py-12">
                <div class="text-gray-400 text-2xl mb-2">⚡</div>
                <div class="text-sm text-gray-500">事件配置功能即将上线</div>
                <div class="text-xs text-gray-400 mt-1">敬请期待</div>
              </div>
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
  </div>
</template>

<style scoped>
.property-tabs :deep(.ant-tabs-nav) {
  margin-bottom: 0;
  padding: 0 16px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.property-tabs :deep(.ant-tabs-tab) {
  padding: 8px 12px;
  margin: 0 4px;
  border-radius: 6px 6px 0 0;
}

.property-tabs :deep(.ant-tabs-tab-active) {
  background: white;
  border-bottom: 1px solid white;
  margin-bottom: -1px;
}

.property-tabs :deep(.ant-tabs-content-holder) {
  background: white;
}

.property-tabs :deep(.ant-form-item-label > label) {
  font-weight: 500;
  color: #374151;
}

.property-tabs :deep(.ant-input),
.property-tabs :deep(.ant-input-number),
.property-tabs :deep(.ant-select-selector) {
  border-radius: 6px;
  border-color: #d1d5db;
}

.property-tabs :deep(.ant-input:focus),
.property-tabs :deep(.ant-input-number:focus),
.property-tabs :deep(.ant-select-focused .ant-select-selector) {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}
</style>
