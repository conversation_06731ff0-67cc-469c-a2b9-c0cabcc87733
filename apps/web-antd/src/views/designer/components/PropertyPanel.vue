<script setup lang="ts">
import { ref, computed, watch, h } from 'vue';
import {
  Card,
  Form,
  FormItem,
  Input,
  InputNumber,
  Select,
  Switch,
  Tabs,
  Empty,
  Divider
} from 'ant-design-vue';
import { <PERSON>diCog, MdiPalette, MdiLink } from '@vben/icons';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

interface Props {
  selectedComponent?: any;
}

interface Emits {
  (e: 'property-change', componentId: string, property: string, value: any): void;
  (e: 'style-change', componentId: string, style: Record<string, any>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 当前激活的标签页
const activeTab = ref('properties');

// 组件属性表单数据
const componentProps = ref<Record<string, any>>({});

// 组件样式表单数据
const componentStyles = ref<Record<string, any>>({});

// 监听选中组件变化
watch(
  () => props.selectedComponent,
  (newComponent) => {
    if (newComponent) {
      componentProps.value = { ...newComponent.props };
      componentStyles.value = { ...newComponent.style };
    } else {
      componentProps.value = {};
      componentStyles.value = {};
    }
  },
  { immediate: true }
);

// 获取组件属性配置
const getComponentPropertyConfig = (componentType: string) => {
  const configs: Record<string, any[]> = {
    button: [
      { key: 'text', label: '按钮文字', type: 'input', defaultValue: '按钮' },
      { key: 'type', label: '按钮类型', type: 'select', options: [
        { label: '默认', value: 'default' },
        { label: '主要', value: 'primary' },
        { label: '成功', value: 'success' },
        { label: '警告', value: 'warning' },
        { label: '危险', value: 'danger' },
      ], defaultValue: 'default' },
      { key: 'disabled', label: '禁用状态', type: 'switch', defaultValue: false },
      { key: 'loading', label: '加载状态', type: 'switch', defaultValue: false },
    ],
    text: [
      { key: 'content', label: '文本内容', type: 'textarea', defaultValue: '文本内容' },
      { key: 'fontSize', label: '字体大小', type: 'number', defaultValue: 14, unit: 'px' },
      { key: 'fontWeight', label: '字体粗细', type: 'select', options: [
        { label: '正常', value: 'normal' },
        { label: '粗体', value: 'bold' },
        { label: '细体', value: 'lighter' },
      ], defaultValue: 'normal' },
      { key: 'textAlign', label: '文本对齐', type: 'select', options: [
        { label: '左对齐', value: 'left' },
        { label: '居中', value: 'center' },
        { label: '右对齐', value: 'right' },
      ], defaultValue: 'left' },
    ],
    input: [
      { key: 'placeholder', label: '占位符', type: 'input', defaultValue: '请输入内容' },
      { key: 'disabled', label: '禁用状态', type: 'switch', defaultValue: false },
      { key: 'readonly', label: '只读状态', type: 'switch', defaultValue: false },
      { key: 'maxLength', label: '最大长度', type: 'number', defaultValue: null },
    ],
    image: [
      { key: 'src', label: '图片地址', type: 'input', defaultValue: '' },
      { key: 'alt', label: '替代文字', type: 'input', defaultValue: '' },
      { key: 'fit', label: '填充模式', type: 'select', options: [
        { label: '填充', value: 'fill' },
        { label: '包含', value: 'contain' },
        { label: '覆盖', value: 'cover' },
        { label: '缩放', value: 'scale-down' },
      ], defaultValue: 'fill' },
    ],
  };
  
  return configs[componentType] || [];
};

// 获取样式配置
const getStyleConfig = () => [
  { key: 'width', label: '宽度', type: 'input', unit: 'px' },
  { key: 'height', label: '高度', type: 'input', unit: 'px' },
  { key: 'backgroundColor', label: '背景颜色', type: 'color' },
  { key: 'color', label: '文字颜色', type: 'color' },
  { key: 'borderRadius', label: '圆角', type: 'number', unit: 'px' },
  { key: 'padding', label: '内边距', type: 'input', unit: 'px' },
  { key: 'margin', label: '外边距', type: 'input', unit: 'px' },
  { key: 'border', label: '边框', type: 'input' },
];

// 处理属性变化
const handlePropertyChange = (key: string, value: any) => {
  if (!props.selectedComponent) return;
  
  componentProps.value[key] = value;
  emit('property-change', props.selectedComponent.id, key, value);
};

// 处理样式变化
const handleStyleChange = (key: string, value: any) => {
  if (!props.selectedComponent) return;
  
  componentStyles.value[key] = value;
  emit('style-change', props.selectedComponent.id, { [key]: value });
};

// 渲染表单项
const renderFormItem = (config: any, value: any, onChange: (value: any) => void) => {
  switch (config.type) {
    case 'input':
      return h(Input, {
        value,
        placeholder: config.placeholder || `请输入${config.label}`,
        'onUpdate:value': onChange,
      });
    
    case 'textarea':
      return h(TextArea, {
        value,
        placeholder: config.placeholder || `请输入${config.label}`,
        rows: 3,
        'onUpdate:value': onChange,
      });
    
    case 'number':
      return h(InputNumber, {
        value,
        min: config.min,
        max: config.max,
        'onUpdate:value': onChange,
      });
    
    case 'select':
      return h(Select, {
        value,
        placeholder: `请选择${config.label}`,
        'onUpdate:value': onChange,
      }, () => config.options?.map((option: any) => 
        h(Option, { key: option.value, value: option.value }, () => option.label)
      ));
    
    case 'switch':
      return h(Switch, {
        checked: value,
        'onUpdate:checked': onChange,
      });
    
    case 'color':
      return h(Input, {
        value,
        'onUpdate:value': onChange,
        placeholder: '#ffffff',
        style: { width: '100%' },
      });
    
    default:
      return h(Input, {
        value,
        'onUpdate:value': onChange,
      });
  }
};

// 计算当前组件的属性配置
const currentPropertyConfig = computed(() => {
  if (!props.selectedComponent) return [];
  return getComponentPropertyConfig(props.selectedComponent.type);
});

// 计算样式配置
const styleConfig = computed(() => getStyleConfig());
</script>

<template>
  <div class="h-full flex flex-col">
    <div class="p-4 border-b">
      <h3 class="text-lg font-semibold text-gray-800">属性面板</h3>
    </div>

    <div class="flex-1 overflow-auto">
      <!-- 未选中组件时的提示 -->
      <div v-if="!selectedComponent" class="flex items-center justify-center h-full">
        <Empty
          :image="Empty.PRESENTED_IMAGE_SIMPLE"
          description="请选择一个组件"
        >
          <template #description>
            <span class="text-gray-500 text-center">
              在画布中点击组件<br>即可编辑其属性
            </span>
          </template>
        </Empty>
      </div>

      <!-- 选中组件时的属性编辑 -->
      <div v-else class="p-4">
        <div class="mb-4 p-3 bg-gray-50 rounded-lg">
          <div class="text-sm font-medium text-gray-900">{{ selectedComponent.type }}</div>
          <div class="text-xs text-gray-500">ID: {{ selectedComponent.id }}</div>
        </div>

        <Tabs v-model:activeKey="activeTab" size="small">
          <!-- 属性配置 -->
          <TabPane key="properties" tab="属性">
            <template #tab>
              <span class="flex items-center">
                <MdiCog class="mr-1" />
                属性
              </span>
            </template>

            <div class="mt-4">
              <Form layout="vertical" size="small">
                <FormItem
                  v-for="config in currentPropertyConfig"
                  :key="config.key"
                  :label="config.label"
                >
                  <div class="flex items-center">
                    <component
                      :is="renderFormItem(
                        config,
                        componentProps[config.key] ?? config.defaultValue,
                        (value) => handlePropertyChange(config.key, value)
                      )"
                      class="flex-1"
                    />
                    <span v-if="config.unit" class="ml-2 text-xs text-gray-500">{{ config.unit }}</span>
                  </div>
                </FormItem>

                <div v-if="currentPropertyConfig.length === 0" class="text-center py-8 text-gray-500">
                  <span>该组件暂无可配置属性</span>
                </div>
              </Form>
            </div>
          </TabPane>

          <!-- 样式配置 -->
          <TabPane key="styles" tab="样式">
            <template #tab>
              <span class="flex items-center">
                <MdiPalette class="mr-1" />
                样式
              </span>
            </template>

            <div class="mt-4">
              <Form layout="vertical" size="small">
                <FormItem
                  v-for="config in styleConfig"
                  :key="config.key"
                  :label="config.label"
                >
                  <div class="flex items-center">
                    <component
                      :is="renderFormItem(
                        config,
                        componentStyles[config.key],
                        (value) => handleStyleChange(config.key, value)
                      )"
                      class="flex-1"
                    />
                    <span v-if="config.unit" class="ml-2 text-xs text-gray-500">{{ config.unit }}</span>
                  </div>
                </FormItem>
              </Form>
            </div>
          </TabPane>

          <!-- 事件配置 -->
          <TabPane key="events" tab="事件">
            <template #tab>
              <span class="flex items-center">
                <MdiLink class="mr-1" />
                事件
              </span>
            </template>

            <div class="mt-4">
              <div class="text-center py-8 text-gray-500">
                <span>事件配置功能即将上线</span>
              </div>
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
  </div>
</template>

<style scoped>
</style>

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  margin: 20px;
}

.empty-description {
  color: #718096;
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
  font-weight: 500;
}

.property-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.component-info {
  padding: 16px 20px;
  background: rgba(118, 75, 162, 0.08);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  flex-shrink: 0;
  backdrop-filter: blur(10px);
}

.component-type {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 6px;
}

.component-id {
  font-size: 12px;
  color: #718096;
  background: rgba(255, 255, 255, 0.6);
  padding: 4px 8px;
  border-radius: 4px;
  display: inline-block;
  font-weight: 500;
}

.properties-form,
.styles-form,
.events-form {
  padding: 20px;
  height: calc(100vh - 320px);
  overflow-y: auto;
  background: transparent;
}

/* 表单项优化 */
.properties-form :deep(.ant-form-item),
.styles-form :deep(.ant-form-item) {
  margin-bottom: 20px;
}

.properties-form :deep(.ant-form-item-label),
.styles-form :deep(.ant-form-item-label) {
  padding-bottom: 8px;
}

.properties-form :deep(.ant-form-item-label > label),
.styles-form :deep(.ant-form-item-label > label) {
  font-size: 13px;
  font-weight: 600;
  color: #4a5568;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

/* 表单控件优化 */
.properties-form :deep(.ant-input),
.properties-form :deep(.ant-input-number),
.properties-form :deep(.ant-select-selector),
.styles-form :deep(.ant-input),
.styles-form :deep(.ant-input-number),
.styles-form :deep(.ant-select-selector) {
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.properties-form :deep(.ant-input:hover),
.properties-form :deep(.ant-input-number:hover),
.properties-form :deep(.ant-select-selector:hover),
.styles-form :deep(.ant-input:hover),
.styles-form :deep(.ant-input-number:hover),
.styles-form :deep(.ant-select-selector:hover) {
  border-color: rgba(118, 75, 162, 0.3);
  background: rgba(255, 255, 255, 0.95);
}

.properties-form :deep(.ant-input:focus),
.properties-form :deep(.ant-input-number:focus),
.properties-form :deep(.ant-select-focused .ant-select-selector),
.styles-form :deep(.ant-input:focus),
.styles-form :deep(.ant-input-number:focus),
.styles-form :deep(.ant-select-focused .ant-select-selector) {
  border-color: #764ba2;
  box-shadow: 0 0 0 2px rgba(118, 75, 162, 0.2);
  background: rgba(255, 255, 255, 1);
}

/* 开关组件优化 */
.properties-form :deep(.ant-switch),
.styles-form :deep(.ant-switch) {
  background: rgba(0, 0, 0, 0.25);
}

.properties-form :deep(.ant-switch-checked),
.styles-form :deep(.ant-switch-checked) {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

/* 标签页优化 */
.property-editor :deep(.ant-tabs-nav) {
  margin: 0;
  padding: 0 20px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.property-editor :deep(.ant-tabs-tab) {
  padding: 12px 16px;
  margin: 0 4px;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: transparent;
  border: 1px solid transparent;
  font-weight: 500;
}

.property-editor :deep(.ant-tabs-tab:hover) {
  background: rgba(118, 75, 162, 0.1);
  border-color: rgba(118, 75, 162, 0.2);
}

.property-editor :deep(.ant-tabs-tab-active) {
  background: rgba(118, 75, 162, 0.15);
  border-color: rgba(118, 75, 162, 0.3);
  color: #764ba2;
  font-weight: 600;
}

.property-editor :deep(.ant-tabs-ink-bar) {
  display: none;
}

.property-editor :deep(.ant-tabs-content) {
  height: calc(100% - 60px);
}

.property-editor :deep(.ant-tabs-tabpane) {
  height: 100%;
}

.form-item-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.6);
  padding: 12px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.form-item-wrapper:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(118, 75, 162, 0.2);
}

.form-item-wrapper > * {
  flex: 1;
}

.unit-label {
  flex: none;
  font-size: 12px;
  color: #718096;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
  background: rgba(118, 75, 162, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
}

.no-properties {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  margin: 20px;
  border: 2px dashed rgba(118, 75, 162, 0.2);
}

.no-properties-text {
  color: #718096;
  font-size: 14px;
  font-weight: 500;
}

.coming-soon {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  margin: 20px;
  border: 2px dashed rgba(118, 75, 162, 0.2);
  color: #718096;
  font-size: 14px;
  font-weight: 500;
}

/* 滚动条美化 */
.properties-form,
.styles-form,
.events-form {
  scrollbar-width: thin;
  scrollbar-color: rgba(118, 75, 162, 0.2) transparent;
}

.properties-form::-webkit-scrollbar,
.styles-form::-webkit-scrollbar,
.events-form::-webkit-scrollbar {
  width: 4px;
}

.properties-form::-webkit-scrollbar-track,
.styles-form::-webkit-scrollbar-track,
.events-form::-webkit-scrollbar-track {
  background: transparent;
}

.properties-form::-webkit-scrollbar-thumb,
.styles-form::-webkit-scrollbar-thumb,
.events-form::-webkit-scrollbar-thumb {
  background: rgba(118, 75, 162, 0.2);
  border-radius: 2px;
}

.properties-form::-webkit-scrollbar-thumb:hover,
.styles-form::-webkit-scrollbar-thumb:hover,
.events-form::-webkit-scrollbar-thumb:hover {
  background: rgba(118, 75, 162, 0.3);
}
</style>
