<script setup lang="ts">
import { ref, computed } from 'vue';
import { Card, Divider, Button, Space, Tooltip } from 'ant-design-vue';
import { 
  MdiFileDocument, 
  MdiTabUnselected, 
  MdiChevronRight,
  MdiPlus,
  MdiPencil,
  MdiDelete,
  MdiEye
} from '@vben/icons';

interface Props {
  activeMenu: 'page' | 'tabbar';
}

interface Emits {
  (e: 'menu-change', menu: 'page' | 'tabbar'): void;
  (e: 'page-select', pageId: string): void;
  (e: 'page-create'): void;
  (e: 'page-edit', pageId: string): void;
  (e: 'page-delete', pageId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 一级菜单项
const primaryMenuItems = [
  {
    key: 'page',
    title: '页面设计',
    icon: MdiFileDocument,
    description: '管理页面和组件'
  },
  {
    key: 'tabbar',
    title: 'TabBar设计',
    icon: MdiTabUnselected,
    description: '配置底部导航'
  }
];

// 模拟页面数据
const pageList = ref([
  { id: 'home', title: '首页', isActive: true },
  { id: 'search', title: '搜索页', isActive: false },
  { id: 'profile', title: '个人中心', isActive: false }
]);

const currentPageId = ref('home');

// 计算二级菜单标题
const getSecondaryTitle = () => {
  const item = primaryMenuItems.find(item => item.key === props.activeMenu);
  return item?.title || '';
};

// 处理一级菜单点击
const handleMenuClick = (menuKey: 'page' | 'tabbar') => {
  emit('menu-change', menuKey);
};

// 处理页面选择
const handlePageSelect = (pageId: string) => {
  currentPageId.value = pageId;
  emit('page-select', pageId);
};

// 处理页面操作
const handlePageCreate = () => {
  emit('page-create');
};

const handlePageEdit = (pageId: string) => {
  emit('page-edit', pageId);
};

const handlePageDelete = (pageId: string) => {
  emit('page-delete', pageId);
};
</script>

<template>
  <div class="sidebar-menu">
    <!-- 一级菜单 -->
    <div class="primary-menu">
      <div class="menu-header">
        <h3>设计模式</h3>
      </div>
      
      <div
        v-for="item in primaryMenuItems"
        :key="item.key"
        class="menu-item"
        :class="{ active: activeMenu === item.key }"
        @click="handleMenuClick(item.key as 'page' | 'tabbar')"
      >
        <div class="menu-item-content">
          <component :is="item.icon" class="menu-icon" />
          <div class="menu-text">
            <div class="menu-title">{{ item.title }}</div>
            <div class="menu-description">{{ item.description }}</div>
          </div>
          <MdiChevronRight 
            class="expand-icon" 
            :class="{ expanded: activeMenu === item.key }" 
          />
        </div>
      </div>
    </div>

    <Divider class="menu-divider" />

    <!-- 二级菜单 -->
    <div class="secondary-menu">
      <div class="secondary-header">
        <h4>{{ getSecondaryTitle() }}</h4>
      </div>
      
      <!-- 页面设计子菜单 -->
      <div v-if="activeMenu === 'page'" class="page-menu">
        <div class="menu-section">
          <div class="section-header">
            <span class="section-title">页面管理</span>
            <Button
              type="text"
              size="small"
              @click="handlePageCreate"
            >
              <template #icon>
                <MdiPlus />
              </template>
              新建
            </Button>
          </div>
          
          <div class="page-list">
            <div
              v-for="page in pageList"
              :key="page.id"
              class="page-item"
              :class="{ active: currentPageId === page.id }"
              @click="handlePageSelect(page.id)"
            >
              <div class="page-info">
                <MdiFileDocument class="page-icon" />
                <span class="page-title">{{ page.title }}</span>
                <div class="page-status" v-if="page.isActive">
                  <div class="status-dot"></div>
                </div>
              </div>
              
              <div class="page-actions">
                <Tooltip title="编辑页面">
                  <Button
                    type="text"
                    size="small"
                    @click.stop="handlePageEdit(page.id)"
                  >
                    <template #icon>
                      <MdiPencil />
                    </template>
                  </Button>
                </Tooltip>
                <Tooltip title="删除页面">
                  <Button
                    type="text"
                    size="small"
                    danger
                    @click.stop="handlePageDelete(page.id)"
                  >
                    <template #icon>
                      <MdiDelete />
                    </template>
                  </Button>
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- TabBar设计子菜单 -->
      <div v-if="activeMenu === 'tabbar'" class="tabbar-menu">
        <div class="menu-section">
          <div class="section-header">
            <span class="section-title">TabBar配置</span>
          </div>
          
          <div class="tabbar-options">
            <div class="option-item">
              <MdiTabUnselected class="option-icon" />
              <span class="option-title">基础设置</span>
            </div>
            <div class="option-item">
              <MdiEye class="option-icon" />
              <span class="option-title">样式配置</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.sidebar-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px 20px;
  background: transparent;
  position: relative;
}

.primary-menu {
  margin-bottom: 20px;
}

.menu-header {
  margin-bottom: 16px;
}

.menu-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  opacity: 0.8;
}

.menu-item {
  padding: 16px 18px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 8px;
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.menu-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.menu-item:hover::before {
  opacity: 1;
}

.menu-item.active {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
}

.menu-item-content {
  display: flex;
  align-items: center;
  gap: 14px;
  position: relative;
  z-index: 1;
}

.menu-icon {
  font-size: 22px;
  color: #4a5568;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.menu-item.active .menu-icon {
  color: #667eea;
  transform: scale(1.1);
}

.menu-text {
  flex: 1;
  min-width: 0;
}

.menu-title {
  font-size: 15px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
  transition: color 0.3s ease;
}

.menu-item.active .menu-title {
  color: #667eea;
}

.menu-description {
  font-size: 12px;
  color: #718096;
  line-height: 1.4;
}

.expand-icon {
  font-size: 18px;
  color: #a0aec0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.expand-icon.expanded {
  transform: rotate(90deg);
  color: #667eea;
}

.menu-divider {
  margin: 20px 0;
  border-color: rgba(0, 0, 0, 0.06);
}

.secondary-menu {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
}

.secondary-header h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-section {
  margin-bottom: 28px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 0 4px;
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: #4a5568;
  text-transform: uppercase;
  letter-spacing: 0.3px;
}

.page-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.page-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 14px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid transparent;
  background: rgba(255, 255, 255, 0.5);
  position: relative;
  overflow: hidden;
}

.page-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.page-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(102, 126, 234, 0.15);
  transform: translateX(4px);
}

.page-item:hover::before {
  transform: scaleY(1);
}

.page-item.active {
  background: rgba(102, 126, 234, 0.08);
  border-color: rgba(102, 126, 234, 0.2);
  transform: translateX(4px);
}

.page-item.active::before {
  transform: scaleY(1);
}

.page-info {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
}

.page-icon {
  font-size: 16px;
  color: #718096;
  flex-shrink: 0;
  transition: color 0.3s ease;
}

.page-item.active .page-icon {
  color: #667eea;
}

.page-title {
  font-size: 13px;
  color: #2d3748;
  font-weight: 500;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.3s ease;
}

.page-item.active .page-title {
  color: #667eea;
  font-weight: 600;
}

.page-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  box-shadow: 0 0 8px rgba(72, 187, 120, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

.page-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateX(8px);
}

.page-item:hover .page-actions {
  opacity: 1;
  transform: translateX(0);
}

/* 新建按钮优化 */
.section-header :deep(.ant-btn) {
  height: 28px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid rgba(102, 126, 234, 0.2);
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
  transition: all 0.3s ease;
}

.section-header :deep(.ant-btn:hover) {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateY(-1px);
}

/* 页面操作按钮 */
.page-actions :deep(.ant-btn) {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.page-actions :deep(.ant-btn:hover) {
  transform: scale(1.1);
}

.tabbar-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 14px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid transparent;
}

.option-item:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(102, 126, 234, 0.15);
  transform: translateX(4px);
}

.option-icon {
  font-size: 18px;
  color: #4a5568;
  transition: color 0.3s ease;
}

.option-item:hover .option-icon {
  color: #667eea;
}

.option-title {
  font-size: 13px;
  color: #2d3748;
  font-weight: 500;
  transition: color 0.3s ease;
}

.option-item:hover .option-title {
  color: #667eea;
}

/* 滚动条美化 */
.secondary-menu {
  scrollbar-width: thin;
  scrollbar-color: rgba(102, 126, 234, 0.2) transparent;
}

.secondary-menu::-webkit-scrollbar {
  width: 4px;
}

.secondary-menu::-webkit-scrollbar-track {
  background: transparent;
}

.secondary-menu::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.2);
  border-radius: 2px;
}

.secondary-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.3);
}
</style>
