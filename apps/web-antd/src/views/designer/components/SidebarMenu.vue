<script setup lang="ts">
import { ref, computed, h } from 'vue';
import { Card, Divider, Button, Space, Tooltip } from 'ant-design-vue';
import { 
  MdiFileDocument, 
  MdiTabUnselected, 
  MdiChevronRight,
  MdiPlus,
  MdiPencil,
  MdiDelete,
  MdiEye
} from '@vben/icons';

interface Props {
  activeMenu: 'page' | 'tabbar';
}

interface Emits {
  (e: 'menu-change', menu: 'page' | 'tabbar'): void;
  (e: 'page-select', pageId: string): void;
  (e: 'page-create'): void;
  (e: 'page-edit', pageId: string): void;
  (e: 'page-delete', pageId: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 一级菜单项
const primaryMenuItems = [
  {
    key: 'page',
    title: '页面设计',
    icon: MdiFileDocument,
    description: '管理页面和组件'
  },
  {
    key: 'tabbar',
    title: 'TabBar设计',
    icon: MdiTabUnselected,
    description: '配置底部导航'
  }
];

// 模拟页面数据
const pageList = ref([
  { id: 'home', title: '首页', isActive: true },
  { id: 'search', title: '搜索页', isActive: false },
  { id: 'profile', title: '个人中心', isActive: false }
]);

const currentPageId = ref('home');

// 计算二级菜单标题
const getSecondaryTitle = () => {
  const item = primaryMenuItems.find(item => item.key === props.activeMenu);
  return item?.title || '';
};

// 处理一级菜单点击
const handleMenuClick = (menuKey: 'page' | 'tabbar') => {
  emit('menu-change', menuKey);
};

// 处理页面选择
const handlePageSelect = (pageId: string) => {
  currentPageId.value = pageId;
  emit('page-select', pageId);
};

// 处理页面操作
const handlePageCreate = () => {
  emit('page-create');
};

const handlePageEdit = (pageId: string) => {
  emit('page-edit', pageId);
};

const handlePageDelete = (pageId: string) => {
  emit('page-delete', pageId);
};
</script>

<template>
  <div class="sidebar-menu">
    <!-- 一级菜单 -->
    <div class="primary-menu">
      <div class="menu-header">
        <h3>设计模式</h3>
      </div>
      
      <div
        v-for="item in primaryMenuItems"
        :key="item.key"
        class="menu-item"
        :class="{ active: activeMenu === item.key }"
        @click="handleMenuClick(item.key as 'page' | 'tabbar')"
      >
        <div class="menu-item-content">
          <component :is="item.icon" class="menu-icon" />
          <div class="menu-text">
            <div class="menu-title">{{ item.title }}</div>
            <div class="menu-description">{{ item.description }}</div>
          </div>
          <MdiChevronRight 
            class="expand-icon" 
            :class="{ expanded: activeMenu === item.key }" 
          />
        </div>
      </div>
    </div>

    <Divider class="menu-divider" />

    <!-- 二级菜单 -->
    <div class="secondary-menu">
      <div class="secondary-header">
        <h4>{{ getSecondaryTitle() }}</h4>
      </div>
      
      <!-- 页面设计子菜单 -->
      <div v-if="activeMenu === 'page'" class="page-menu">
        <div class="menu-section">
          <div class="section-header">
            <span class="section-title">页面管理</span>
            <Button 
              type="text" 
              size="small"
              :icon="h(MdiPlus)"
              @click="handlePageCreate"
            >
              新建
            </Button>
          </div>
          
          <div class="page-list">
            <div
              v-for="page in pageList"
              :key="page.id"
              class="page-item"
              :class="{ active: currentPageId === page.id }"
              @click="handlePageSelect(page.id)"
            >
              <div class="page-info">
                <MdiFileDocument class="page-icon" />
                <span class="page-title">{{ page.title }}</span>
                <div class="page-status" v-if="page.isActive">
                  <div class="status-dot"></div>
                </div>
              </div>
              
              <div class="page-actions">
                <Tooltip title="编辑页面">
                  <Button
                    type="text"
                    size="small"
                    :icon="h(MdiPencil)"
                    @click.stop="handlePageEdit(page.id)"
                  />
                </Tooltip>
                <Tooltip title="删除页面">
                  <Button
                    type="text"
                    size="small"
                    danger
                    :icon="h(MdiDelete)"
                    @click.stop="handlePageDelete(page.id)"
                  />
                </Tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- TabBar设计子菜单 -->
      <div v-if="activeMenu === 'tabbar'" class="tabbar-menu">
        <div class="menu-section">
          <div class="section-header">
            <span class="section-title">TabBar配置</span>
          </div>
          
          <div class="tabbar-options">
            <div class="option-item">
              <MdiTabUnselected class="option-icon" />
              <span class="option-title">基础设置</span>
            </div>
            <div class="option-item">
              <MdiEye class="option-icon" />
              <span class="option-title">样式配置</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.sidebar-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
}

.primary-menu {
  flex-shrink: 0;
}

.menu-header {
  margin-bottom: 12px;
}

.menu-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: #8c8c8c;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-item {
  margin-bottom: 8px;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.menu-item:hover {
  background: #f5f5f5;
}

.menu-item.active {
  background: #e6f7ff;
  border-color: #1890ff;
}

.menu-item-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.menu-icon {
  font-size: 20px;
  color: #595959;
  flex-shrink: 0;
}

.menu-item.active .menu-icon {
  color: #1890ff;
}

.menu-text {
  flex: 1;
  min-width: 0;
}

.menu-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.menu-description {
  font-size: 12px;
  color: #8c8c8c;
}

.expand-icon {
  font-size: 16px;
  color: #bfbfbf;
  transition: transform 0.2s;
  flex-shrink: 0;
}

.expand-icon.expanded {
  transform: rotate(90deg);
  color: #1890ff;
}

.menu-divider {
  margin: 16px 0;
}

.secondary-menu {
  flex: 1;
  overflow-y: auto;
}

.secondary-header h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.menu-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  font-size: 13px;
  font-weight: 500;
  color: #595959;
}

.page-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.page-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.page-item:hover {
  background: #f5f5f5;
}

.page-item.active {
  background: #e6f7ff;
  border-color: #1890ff;
}

.page-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0;
}

.page-icon {
  font-size: 16px;
  color: #595959;
  flex-shrink: 0;
}

.page-item.active .page-icon {
  color: #1890ff;
}

.page-title {
  font-size: 13px;
  color: #262626;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.page-status {
  display: flex;
  align-items: center;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #52c41a;
}

.page-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.page-item:hover .page-actions {
  opacity: 1;
}

.tabbar-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
}

.option-item:hover {
  background: #f5f5f5;
}

.option-icon {
  font-size: 16px;
  color: #595959;
}

.option-title {
  font-size: 13px;
  color: #262626;
}
</style>
