<script setup lang="ts">
import { ref, computed, nextTick, h } from 'vue';
import { <PERSON>, Button } from 'ant-design-vue';
import { <PERSON>diDele<PERSON>, MdiContentCopy, MdiArrowAll, MdiDragVertical } from '@vben/icons';

// 导入低代码核心渲染器
import { ComponentRenderer } from '@vben/lowcode/core';

interface CanvasComponent {
  id: string;
  type: string;
  props: Record<string, any>;
  style: Record<string, any>;
}

interface Props {
  components: CanvasComponent[];
  selectedComponentId?: string | null;
  scale: number;
  showGrid: boolean;
}

interface Emits {
  (e: 'component-select', component: CanvasComponent | null): void;
  (e: 'component-drop', componentType: string, position: { x: number; y: number }): void;
  (e: 'component-delete', componentId: string): void;
  (e: 'component-update', componentId: string, updates: Partial<CanvasComponent>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const canvasRef = ref<HTMLElement>();
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });

// 计算画布样式
const canvasStyle = computed(() => ({
  transform: `scale(${props.scale})`,
  transformOrigin: 'top left',
}));

// 计算网格样式
const gridStyle = computed(() => {
  if (!props.showGrid) return {};
  
  return {
    backgroundImage: `
      linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)
    `,
    backgroundSize: '20px 20px',
  };
});

// 组件渲染现在使用 ComponentRenderer，不需要手动处理内容

// 获取组件的默认样式
const getComponentStyle = (component: CanvasComponent) => {
  const baseStyle = {
    position: 'absolute',
    minWidth: '60px',
    minHeight: '32px',
    padding: '8px 16px',
    border: '1px solid #d9d9d9',
    borderRadius: '4px',
    background: '#fff',
    cursor: 'pointer',
    userSelect: 'none',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '14px',
    ...component.style,
  };

  // 选中状态样式
  if (props.selectedComponentId === component.id) {
    baseStyle.border = '2px solid #1890ff';
    baseStyle.boxShadow = '0 0 0 2px rgba(24, 144, 255, 0.2)';
  }

  return baseStyle;
};

// 处理画布拖拽放置
const handleCanvasDrop = (event: DragEvent) => {
  event.preventDefault();
  
  const componentType = event.dataTransfer?.getData('component-type');
  if (!componentType || !canvasRef.value) return;

  const rect = canvasRef.value.getBoundingClientRect();
  const position = {
    x: (event.clientX - rect.left) / props.scale,
    y: (event.clientY - rect.top) / props.scale,
  };

  emit('component-drop', componentType, position);
};

const handleCanvasDragOver = (event: DragEvent) => {
  event.preventDefault();
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy';
  }
};

// 处理组件选择
const handleComponentClick = (component: CanvasComponent, event: MouseEvent) => {
  event.stopPropagation();
  emit('component-select', component);
};

// 处理画布点击（取消选择）
const handleCanvasClick = () => {
  emit('component-select', null);
};

// 处理组件删除
const handleComponentDelete = (componentId: string, event: MouseEvent) => {
  event.stopPropagation();
  emit('component-delete', componentId);
};

// 处理组件复制
const handleComponentCopy = (componentId: string, event: MouseEvent) => {
  event.stopPropagation();
  const component = props.components.find(c => c.id === componentId);
  if (component) {
    const newComponent = {
      ...component,
      id: `${component.type}_${Date.now()}`,
      style: {
        ...component.style,
        left: (parseInt(component.style.left || '0') + 20) + 'px',
        top: (parseInt(component.style.top || '0') + 20) + 'px',
      },
    };
    emit('component-drop', component.type, {
      x: parseInt(newComponent.style.left),
      y: parseInt(newComponent.style.top),
    });
  }
};

// 处理组件拖拽移动
const handleComponentMouseDown = (component: CanvasComponent, event: MouseEvent) => {
  if (event.target !== event.currentTarget) return;
  
  event.preventDefault();
  event.stopPropagation();
  
  isDragging.value = true;
  const rect = (event.target as HTMLElement).getBoundingClientRect();
  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top,
  };

  const handleMouseMove = (moveEvent: MouseEvent) => {
    if (!isDragging.value || !canvasRef.value) return;

    const canvasRect = canvasRef.value.getBoundingClientRect();
    const newPosition = {
      x: (moveEvent.clientX - canvasRect.left - dragOffset.value.x) / props.scale,
      y: (moveEvent.clientY - canvasRect.top - dragOffset.value.y) / props.scale,
    };

    emit('component-update', component.id, {
      style: {
        ...component.style,
        left: Math.max(0, newPosition.x) + 'px',
        top: Math.max(0, newPosition.y) + 'px',
      },
    });
  };

  const handleMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};
</script>

<template>
  <div class="design-canvas-container">
    <div
      ref="canvasRef"
      class="design-canvas"
      :style="{ ...canvasStyle, ...gridStyle }"
      @drop="handleCanvasDrop"
      @dragover="handleCanvasDragOver"
      @click="handleCanvasClick"
    >
      <!-- 画布提示 -->
      <div v-if="components.length === 0" class="canvas-placeholder">
        <div class="placeholder-content">
          <div class="placeholder-icon">📱</div>
          <div class="placeholder-text">
            <h3>开始设计你的页面</h3>
            <p>从左侧组件面板拖拽组件到这里</p>
          </div>
        </div>
      </div>

      <!-- 渲染组件 -->
      <div
        v-for="component in components"
        :key="component.id"
        class="canvas-component"
        :class="{
          selected: selectedComponentId === component.id,
          dragging: isDragging && selectedComponentId === component.id
        }"
        :style="getComponentStyle(component)"
        @click="handleComponentClick(component, $event)"
        @mousedown="handleComponentMouseDown(component, $event)"
      >
        <!-- 使用低代码组件渲染器渲染实际组件 -->
        <div class="component-content">
          <ComponentRenderer
            :id="component.id"
            :type="component.type"
            :component-props="component.props"
            :style="component.style"
            :children="component.children"
          />
        </div>

        <!-- 选中状态的操作按钮 -->
        <div
          v-if="selectedComponentId === component.id"
          class="component-actions"
          @click.stop
        >
          <Button
            type="text"
            size="small"
            class="action-btn move-btn"
            title="拖拽移动"
          >
            <template #icon>
              <MdiDragVertical />
            </template>
          </Button>
          <Button
            type="text"
            size="small"
            class="action-btn"
            title="复制组件"
            @click="handleComponentCopy(component.id, $event)"
          >
            <template #icon>
              <MdiContentCopy />
            </template>
          </Button>
          <Button
            type="text"
            size="small"
            danger
            class="action-btn"
            title="删除组件"
            @click="handleComponentDelete(component.id, $event)"
          >
            <template #icon>
              <MdiDelete />
            </template>
          </Button>
        </div>

        <!-- 组件类型标签 -->
        <div class="component-label">
          {{ component.type }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 设计画布容器 */
.design-canvas-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: transparent;
  border-radius: 16px;
  position: relative;
}

.design-canvas-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.03) 0%, transparent 50%);
  pointer-events: none;
}

/* 设计画布 - 手机模拟器 */
.design-canvas {
  position: relative;
  width: 375px; /* 模拟手机屏幕宽度 */
  height: 667px; /* 模拟手机屏幕高度 */
  margin: 40px auto;
  background: #fff;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 20px;
  overflow: hidden;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  z-index: 1;
}

.design-canvas::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
  z-index: 10;
}

/* 画布占位符 */
.canvas-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #718096;
  z-index: 1;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 2px dashed rgba(102, 126, 234, 0.2);
}

.placeholder-icon {
  font-size: 64px;
  opacity: 0.4;
  color: #667eea;
}

.placeholder-text h3 {
  margin: 0;
  font-size: 18px;
  color: #2d3748;
  font-weight: 600;
}

.placeholder-text p {
  margin: 8px 0 0 0;
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

/* 画布组件 */
.canvas-component {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  border-radius: 8px;
  overflow: hidden;
}

.canvas-component:hover {
  z-index: 10;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.canvas-component.selected {
  z-index: 20;
  box-shadow: 0 0 0 2px #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.canvas-component.dragging {
  z-index: 30;
  opacity: 0.8;
  transform: rotate(2deg) scale(1.02);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.3);
}

.component-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

/* 组件操作按钮 */
.component-actions {
  position: absolute;
  top: -36px;
  right: 0;
  display: flex;
  gap: 6px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0;
  transform: translateY(4px);
  transition: all 0.3s ease;
}

.canvas-component:hover .component-actions,
.canvas-component.selected .component-actions {
  opacity: 1;
  transform: translateY(0);
}

.action-btn {
  width: 28px;
  height: 28px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.action-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.2);
  transform: scale(1.1);
}

.move-btn {
  cursor: move;
  color: #4a5568;
}

.move-btn:hover {
  color: #667eea;
}

/* 组件标签 */
.component-label {
  position: absolute;
  top: -20px;
  left: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  font-size: 11px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  line-height: 1;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  opacity: 0;
  transform: translateY(4px);
  transition: all 0.3s ease;
}

.canvas-component:hover .component-label,
.canvas-component.selected .component-label {
  opacity: 1;
  transform: translateY(0);
}

/* 滚动条美化 */
.design-canvas-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(102, 126, 234, 0.2) transparent;
}

.design-canvas-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.design-canvas-container::-webkit-scrollbar-track {
  background: transparent;
}

.design-canvas-container::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.2);
  border-radius: 3px;
}

.design-canvas-container::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.3);
}

.design-canvas-container::-webkit-scrollbar-corner {
  background: transparent;
}
</style>
