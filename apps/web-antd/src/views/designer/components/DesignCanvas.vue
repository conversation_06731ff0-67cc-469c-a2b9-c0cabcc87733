<script setup lang="ts">
import { ref, computed, nextTick, h } from 'vue';
import { <PERSON>, Button } from 'ant-design-vue';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, MdiContentCopy, MdiArrowAll } from '@vben/icons';

interface CanvasComponent {
  id: string;
  type: string;
  props: Record<string, any>;
  style: Record<string, any>;
}

interface Props {
  components: CanvasComponent[];
  selectedComponentId?: string | null;
  scale: number;
  showGrid: boolean;
}

interface Emits {
  (e: 'component-select', component: CanvasComponent | null): void;
  (e: 'component-drop', componentType: string, position: { x: number; y: number }): void;
  (e: 'component-delete', componentId: string): void;
  (e: 'component-update', componentId: string, updates: Partial<CanvasComponent>): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const canvasRef = ref<HTMLElement>();
const isDragging = ref(false);
const dragOffset = ref({ x: 0, y: 0 });

// 计算画布样式
const canvasStyle = computed(() => ({
  transform: `scale(${props.scale})`,
  transformOrigin: 'top left',
}));

// 计算网格样式
const gridStyle = computed(() => {
  if (!props.showGrid) return {};
  
  return {
    backgroundImage: `
      linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)
    `,
    backgroundSize: '20px 20px',
  };
});

// 获取组件的渲染内容
const getComponentContent = (component: CanvasComponent) => {
  switch (component.type) {
    case 'button':
      return component.props.text || '按钮';
    case 'text':
      return component.props.content || '文本内容';
    case 'input':
      return '输入框';
    case 'image':
      return '图片';
    case 'container':
      return '容器';
    default:
      return component.type;
  }
};

// 获取组件的默认样式
const getComponentStyle = (component: CanvasComponent) => {
  const baseStyle = {
    position: 'absolute',
    minWidth: '60px',
    minHeight: '32px',
    padding: '8px 16px',
    border: '1px solid #d9d9d9',
    borderRadius: '4px',
    background: '#fff',
    cursor: 'pointer',
    userSelect: 'none',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '14px',
    ...component.style,
  };

  // 选中状态样式
  if (props.selectedComponentId === component.id) {
    baseStyle.border = '2px solid #1890ff';
    baseStyle.boxShadow = '0 0 0 2px rgba(24, 144, 255, 0.2)';
  }

  return baseStyle;
};

// 处理画布拖拽放置
const handleCanvasDrop = (event: DragEvent) => {
  event.preventDefault();
  
  const componentType = event.dataTransfer?.getData('component-type');
  if (!componentType || !canvasRef.value) return;

  const rect = canvasRef.value.getBoundingClientRect();
  const position = {
    x: (event.clientX - rect.left) / props.scale,
    y: (event.clientY - rect.top) / props.scale,
  };

  emit('component-drop', componentType, position);
};

const handleCanvasDragOver = (event: DragEvent) => {
  event.preventDefault();
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy';
  }
};

// 处理组件选择
const handleComponentClick = (component: CanvasComponent, event: MouseEvent) => {
  event.stopPropagation();
  emit('component-select', component);
};

// 处理画布点击（取消选择）
const handleCanvasClick = () => {
  emit('component-select', null);
};

// 处理组件删除
const handleComponentDelete = (componentId: string, event: MouseEvent) => {
  event.stopPropagation();
  emit('component-delete', componentId);
};

// 处理组件拖拽移动
const handleComponentMouseDown = (component: CanvasComponent, event: MouseEvent) => {
  if (event.target !== event.currentTarget) return;
  
  event.preventDefault();
  event.stopPropagation();
  
  isDragging.value = true;
  const rect = (event.target as HTMLElement).getBoundingClientRect();
  dragOffset.value = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top,
  };

  const handleMouseMove = (moveEvent: MouseEvent) => {
    if (!isDragging.value || !canvasRef.value) return;

    const canvasRect = canvasRef.value.getBoundingClientRect();
    const newPosition = {
      x: (moveEvent.clientX - canvasRect.left - dragOffset.value.x) / props.scale,
      y: (moveEvent.clientY - canvasRect.top - dragOffset.value.y) / props.scale,
    };

    emit('component-update', component.id, {
      style: {
        ...component.style,
        left: Math.max(0, newPosition.x) + 'px',
        top: Math.max(0, newPosition.y) + 'px',
      },
    });
  };

  const handleMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', handleMouseMove);
    document.removeEventListener('mouseup', handleMouseUp);
  };

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', handleMouseUp);
};
</script>

<template>
  <div class="design-canvas-container">
    <div
      ref="canvasRef"
      class="design-canvas"
      :style="{ ...canvasStyle, ...gridStyle }"
      @drop="handleCanvasDrop"
      @dragover="handleCanvasDragOver"
      @click="handleCanvasClick"
    >
      <!-- 画布提示 -->
      <div v-if="components.length === 0" class="canvas-placeholder">
        <div class="placeholder-content">
          <div class="placeholder-icon">📱</div>
          <div class="placeholder-text">
            <h3>开始设计你的页面</h3>
            <p>从左侧组件面板拖拽组件到这里</p>
          </div>
        </div>
      </div>

      <!-- 渲染组件 -->
      <div
        v-for="component in components"
        :key="component.id"
        class="canvas-component"
        :class="{ 
          selected: selectedComponentId === component.id,
          dragging: isDragging && selectedComponentId === component.id 
        }"
        :style="getComponentStyle(component)"
        @click="handleComponentClick(component, $event)"
        @mousedown="handleComponentMouseDown(component, $event)"
      >
        <!-- 组件内容 -->
        <div class="component-content">
          {{ getComponentContent(component) }}
        </div>

        <!-- 选中状态的操作按钮 -->
        <div 
          v-if="selectedComponentId === component.id" 
          class="component-actions"
          @click.stop
        >
          <Button
            type="text"
            size="small"
            :icon="h(MdiArrowAll)"
            class="action-btn move-btn"
            title="拖拽移动"
          />
          <Button
            type="text"
            size="small"
            :icon="h(MdiContentCopy)"
            class="action-btn"
            title="复制组件"
          />
          <Button
            type="text"
            size="small"
            danger
            :icon="h(MdiDelete)"
            class="action-btn"
            title="删除组件"
            @click="handleComponentDelete(component.id, $event)"
          />
        </div>

        <!-- 组件类型标签 -->
        <div class="component-label">
          {{ component.type }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.design-canvas-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.design-canvas {
  position: relative;
  width: 375px; /* 模拟手机屏幕宽度 */
  height: 667px; /* 模拟手机屏幕高度 */
  margin: 20px auto;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.canvas-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #8c8c8c;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.placeholder-icon {
  font-size: 48px;
  opacity: 0.5;
}

.placeholder-text h3 {
  margin: 0;
  font-size: 16px;
  color: #595959;
}

.placeholder-text p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: #8c8c8c;
}

.canvas-component {
  transition: all 0.2s ease;
  position: relative;
}

.canvas-component:hover {
  z-index: 10;
}

.canvas-component.selected {
  z-index: 20;
}

.canvas-component.dragging {
  z-index: 30;
  opacity: 0.8;
  transform: rotate(2deg);
}

.component-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
}

.component-actions {
  position: absolute;
  top: -32px;
  right: 0;
  display: flex;
  gap: 4px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn {
  width: 24px;
  height: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.move-btn {
  cursor: move;
}

.component-label {
  position: absolute;
  top: -16px;
  left: 0;
  background: #1890ff;
  color: #fff;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 2px;
  line-height: 1;
  white-space: nowrap;
}

/* 滚动条样式 */
.design-canvas-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.design-canvas-container::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4px;
}

.design-canvas-container::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 4px;
}

.design-canvas-container::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
</style>
