<script setup lang="ts">
import { ref, computed, h } from 'vue';
import {
  Card,
  Form,
  FormItem,
  Input,
  Select,
  Switch,
  Button,
  Space,
  Divider,
  InputNumber,
  Tabs,
  Table
} from 'ant-design-vue';
import { 
  MdiPlus, 
  MdiDelete, 
  MdiPencil,
  MdiHome,
  MdiMagnify,
  MdiAccount,
  MdiCog,
  MdiHeart
} from '@vben/icons';

const { TabPane } = Tabs;
const { Option } = Select;

interface TabBarItem {
  id: string;
  name: string;
  title: string;
  icon: string;
  pageId: string;
  badge?: string;
}

interface TabBarConfig {
  position: 'bottom' | 'top';
  backgroundColor: string;
  activeColor: string;
  inactiveColor: string;
  borderTop: boolean;
  safeAreaInsetBottom: boolean;
  fixed: boolean;
  zIndex: number;
}

interface Props {
  selectedComponent?: any;
}

interface Emits {
  (e: 'component-select', component: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 当前激活的标签页
const activeTab = ref('config');

// TabBar配置
const tabBarConfig = ref<TabBarConfig>({
  position: 'bottom',
  backgroundColor: '#ffffff',
  activeColor: '#1890ff',
  inactiveColor: '#8c8c8c',
  borderTop: true,
  safeAreaInsetBottom: true,
  fixed: true,
  zIndex: 1000,
});

// TabBar项目列表
const tabBarItems = ref<TabBarItem[]>([
  { id: '1', name: 'home', title: '首页', icon: 'mdi:home', pageId: 'home' },
  { id: '2', name: 'search', title: '搜索', icon: 'mdi:magnify', pageId: 'search' },
  { id: '3', name: 'profile', title: '我的', icon: 'mdi:account', pageId: 'profile' },
]);

// 可用页面列表
const availablePages = ref([
  { id: 'home', title: '首页' },
  { id: 'search', title: '搜索页' },
  { id: 'profile', title: '个人中心' },
  { id: 'settings', title: '设置页' },
]);

// 可用图标列表
const availableIcons = [
  { value: 'mdi:home', label: '首页', icon: MdiHome },
  { value: 'mdi:magnify', label: '搜索', icon: MdiMagnify },
  { value: 'mdi:account', label: '用户', icon: MdiAccount },
  { value: 'mdi:cog', label: '设置', icon: MdiCog },
  { value: 'mdi:heart', label: '收藏', icon: MdiHeart },
];

// 表格列定义
const columns = [
  { title: '名称', dataIndex: 'name', key: 'name' },
  { title: '标题', dataIndex: 'title', key: 'title' },
  { title: '图标', dataIndex: 'icon', key: 'icon' },
  { title: '绑定页面', dataIndex: 'pageId', key: 'pageId' },
  { title: '操作', key: 'action', width: 120 },
];

// 添加TabBar项目
const handleAddItem = () => {
  const newItem: TabBarItem = {
    id: Date.now().toString(),
    name: `tab_${tabBarItems.value.length + 1}`,
    title: '新标签',
    icon: 'mdi:home',
    pageId: availablePages.value[0]?.id || '',
  };
  tabBarItems.value.push(newItem);
};

// 删除TabBar项目
const handleDeleteItem = (id: string) => {
  const index = tabBarItems.value.findIndex(item => item.id === id);
  if (index > -1) {
    tabBarItems.value.splice(index, 1);
  }
};

// 编辑TabBar项目
const handleEditItem = (id: string) => {
  console.log('编辑项目:', id);
};

// 获取页面标题
const getPageTitle = (pageId: string) => {
  const page = availablePages.value.find(p => p.id === pageId);
  return page?.title || pageId;
};

// 获取图标组件
const getIconComponent = (iconValue: string) => {
  const iconItem = availableIcons.find(icon => icon.value === iconValue);
  return iconItem?.icon || MdiHome;
};

// 预览样式
const previewStyle = computed(() => ({
  position: 'relative',
  width: '375px',
  height: '60px',
  backgroundColor: tabBarConfig.value.backgroundColor,
  borderTop: tabBarConfig.value.borderTop ? '1px solid #e8e8e8' : 'none',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-around',
  margin: '20px auto',
  borderRadius: '8px',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
}));
</script>

<template>
  <div class="tabbar-designer">
    <div class="designer-content">
      <Tabs v-model:activeKey="activeTab" type="card">
        <!-- TabBar配置 -->
        <TabPane key="config" tab="基础配置">
          <div class="config-section">
            <Card title="TabBar设置" size="small">
              <Form layout="vertical">
                <FormItem label="位置">
                  <Select v-model:value="tabBarConfig.position">
                    <Option value="bottom">底部</Option>
                    <Option value="top">顶部</Option>
                  </Select>
                </FormItem>
                
                <FormItem label="背景颜色">
                  <Input
                    v-model:value="tabBarConfig.backgroundColor"
                    placeholder="#ffffff"
                    style="width: 100%"
                  />
                </FormItem>

                <FormItem label="激活颜色">
                  <Input
                    v-model:value="tabBarConfig.activeColor"
                    placeholder="#1890ff"
                    style="width: 100%"
                  />
                </FormItem>

                <FormItem label="非激活颜色">
                  <Input
                    v-model:value="tabBarConfig.inactiveColor"
                    placeholder="#8c8c8c"
                    style="width: 100%"
                  />
                </FormItem>
                
                <FormItem label="层级">
                  <InputNumber 
                    v-model:value="tabBarConfig.zIndex" 
                    :min="1" 
                    :max="9999" 
                  />
                </FormItem>
                
                <FormItem>
                  <Space direction="vertical">
                    <Switch 
                      v-model:checked="tabBarConfig.borderTop"
                      checked-children="显示上边框"
                      un-checked-children="隐藏上边框"
                    />
                    <Switch 
                      v-model:checked="tabBarConfig.fixed"
                      checked-children="固定定位"
                      un-checked-children="相对定位"
                    />
                    <Switch 
                      v-model:checked="tabBarConfig.safeAreaInsetBottom"
                      checked-children="适配安全区域"
                      un-checked-children="不适配安全区域"
                    />
                  </Space>
                </FormItem>
              </Form>
            </Card>
          </div>
        </TabPane>

        <!-- TabBar项目管理 -->
        <TabPane key="items" tab="项目管理">
          <div class="items-section">
            <Card 
              title="TabBar项目" 
              size="small"
              :extra="h(Button, { 
                type: 'primary', 
                size: 'small',
                icon: h(MdiPlus),
                onClick: handleAddItem 
              }, () => '添加项目')"
            >
              <Table
                :dataSource="tabBarItems"
                :columns="columns"
                :pagination="false"
                size="small"
                rowKey="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'icon'">
                    <Space>
                      <component :is="getIconComponent(record.icon)" />
                      <span>{{ record.icon }}</span>
                    </Space>
                  </template>
                  
                  <template v-else-if="column.key === 'pageId'">
                    {{ getPageTitle(record.pageId) }}
                  </template>
                  
                  <template v-else-if="column.key === 'action'">
                    <Space>
                      <Button
                        type="text"
                        size="small"
                        :icon="h(MdiPencil)"
                        @click="handleEditItem(record.id)"
                      >
                        编辑
                      </Button>
                      <Button
                        type="text"
                        size="small"
                        danger
                        :icon="h(MdiDelete)"
                        @click="handleDeleteItem(record.id)"
                      >
                        删除
                      </Button>
                    </Space>
                  </template>
                </template>
              </Table>
            </Card>
          </div>
        </TabPane>

        <!-- 预览 -->
        <TabPane key="preview" tab="预览效果">
          <div class="preview-section">
            <Card title="TabBar预览" size="small">
              <div class="preview-container">
                <div class="phone-mockup">
                  <div class="phone-screen">
                    <div class="screen-content">
                      <div class="content-placeholder">
                        <h3>页面内容区域</h3>
                        <p>TabBar将显示在页面底部</p>
                      </div>
                    </div>
                    
                    <!-- TabBar预览 -->
                    <div class="tabbar-preview" :style="previewStyle">
                      <div
                        v-for="item in tabBarItems"
                        :key="item.id"
                        class="tab-item"
                        :style="{
                          color: item.id === '1' ? tabBarConfig.activeColor : tabBarConfig.inactiveColor
                        }"
                      >
                        <div class="tab-icon">
                          <component :is="getIconComponent(item.icon)" />
                        </div>
                        <div class="tab-title">{{ item.title }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>

<style scoped>
/* TabBar设计器主容器 */
.tabbar-designer {
  height: 100%;
  padding: 24px;
  background: transparent;
  position: relative;
}

.tabbar-designer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 30%, rgba(118, 75, 162, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 70% 70%, rgba(102, 126, 234, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.designer-content {
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
}

/* 标签页优化 */
.designer-content :deep(.ant-tabs-nav) {
  margin: 0;
  padding: 0 24px;
  background: linear-gradient(135deg, rgba(118, 75, 162, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.designer-content :deep(.ant-tabs-tab) {
  padding: 16px 20px;
  margin: 0 4px;
  border-radius: 12px 12px 0 0;
  transition: all 0.3s ease;
  background: transparent;
  border: 1px solid transparent;
  font-weight: 500;
  font-size: 14px;
}

.designer-content :deep(.ant-tabs-tab:hover) {
  background: rgba(118, 75, 162, 0.1);
  border-color: rgba(118, 75, 162, 0.2);
  transform: translateY(-2px);
}

.designer-content :deep(.ant-tabs-tab-active) {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(118, 75, 162, 0.3);
  color: #764ba2;
  font-weight: 600;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(118, 75, 162, 0.15);
}

.designer-content :deep(.ant-tabs-ink-bar) {
  display: none;
}

/* 内容区域 */
.config-section,
.items-section,
.preview-section {
  padding: 24px;
  height: calc(100vh - 240px);
  overflow-y: auto;
  background: transparent;
}

/* 卡片优化 */
.config-section :deep(.ant-card),
.items-section :deep(.ant-card),
.preview-section :deep(.ant-card) {
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  margin-bottom: 20px;
}

.config-section :deep(.ant-card-head),
.items-section :deep(.ant-card-head),
.preview-section :deep(.ant-card-head) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, rgba(118, 75, 162, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);
}

.config-section :deep(.ant-card-head-title),
.items-section :deep(.ant-card-head-title),
.preview-section :deep(.ant-card-head-title) {
  font-weight: 600;
  color: #2d3748;
}

/* 表单优化 */
.config-section :deep(.ant-form-item-label > label) {
  font-size: 13px;
  font-weight: 600;
  color: #4a5568;
}

.config-section :deep(.ant-input),
.config-section :deep(.ant-input-number),
.config-section :deep(.ant-select-selector) {
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.config-section :deep(.ant-input:hover),
.config-section :deep(.ant-input-number:hover),
.config-section :deep(.ant-select-selector:hover) {
  border-color: rgba(118, 75, 162, 0.3);
}

.config-section :deep(.ant-input:focus),
.config-section :deep(.ant-input-number:focus),
.config-section :deep(.ant-select-focused .ant-select-selector) {
  border-color: #764ba2;
  box-shadow: 0 0 0 2px rgba(118, 75, 162, 0.2);
}

.config-section :deep(.ant-switch) {
  background: rgba(0, 0, 0, 0.25);
}

.config-section :deep(.ant-switch-checked) {
  background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

/* 预览容器 */
.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 500px;
  padding: 40px;
}

.phone-mockup {
  width: 375px;
  height: 667px;
  background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
  border-radius: 24px;
  padding: 12px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
}

.phone-mockup::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.screen-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.content-placeholder {
  text-align: center;
  color: #718096;
}

.content-placeholder h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #2d3748;
  font-weight: 600;
}

.content-placeholder p {
  margin: 0;
  font-size: 14px;
  opacity: 0.8;
}

.tabbar-preview {
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  flex: 1;
  padding: 12px 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.tab-item:hover {
  background: rgba(118, 75, 162, 0.05);
}

.tab-icon {
  font-size: 22px;
  line-height: 1;
  transition: transform 0.3s ease;
}

.tab-item:hover .tab-icon {
  transform: scale(1.1);
}

.tab-title {
  font-size: 11px;
  line-height: 1.2;
  text-align: center;
  font-weight: 500;
}

/* 滚动条美化 */
.config-section,
.items-section,
.preview-section {
  scrollbar-width: thin;
  scrollbar-color: rgba(118, 75, 162, 0.2) transparent;
}

.config-section::-webkit-scrollbar,
.items-section::-webkit-scrollbar,
.preview-section::-webkit-scrollbar {
  width: 4px;
}

.config-section::-webkit-scrollbar-track,
.items-section::-webkit-scrollbar-track,
.preview-section::-webkit-scrollbar-track {
  background: transparent;
}

.config-section::-webkit-scrollbar-thumb,
.items-section::-webkit-scrollbar-thumb,
.preview-section::-webkit-scrollbar-thumb {
  background: rgba(118, 75, 162, 0.2);
  border-radius: 2px;
}

.config-section::-webkit-scrollbar-thumb:hover,
.items-section::-webkit-scrollbar-thumb:hover,
.preview-section::-webkit-scrollbar-thumb:hover {
  background: rgba(118, 75, 162, 0.3);
}
</style>
