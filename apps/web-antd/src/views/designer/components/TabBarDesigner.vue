<script setup lang="ts">
import { ref, computed, h } from 'vue';
import { 
  Card, 
  Form, 
  FormItem, 
  Input, 
  Select, 
  Switch, 
  Button, 
  Space, 
  Divider,
  ColorPicker,
  InputNumber,
  Tabs,
  Table
} from 'ant-design-vue';
import { 
  MdiPlus, 
  MdiDelete, 
  MdiPencil,
  MdiHome,
  MdiMagnify,
  MdiAccount,
  MdiCog,
  MdiHeart
} from '@vben/icons';

const { TabPane } = Tabs;
const { Option } = Select;

interface TabBarItem {
  id: string;
  name: string;
  title: string;
  icon: string;
  pageId: string;
  badge?: string;
}

interface TabBarConfig {
  position: 'bottom' | 'top';
  backgroundColor: string;
  activeColor: string;
  inactiveColor: string;
  borderTop: boolean;
  safeAreaInsetBottom: boolean;
  fixed: boolean;
  zIndex: number;
}

interface Props {
  selectedComponent?: any;
}

interface Emits {
  (e: 'component-select', component: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 当前激活的标签页
const activeTab = ref('config');

// TabBar配置
const tabBarConfig = ref<TabBarConfig>({
  position: 'bottom',
  backgroundColor: '#ffffff',
  activeColor: '#1890ff',
  inactiveColor: '#8c8c8c',
  borderTop: true,
  safeAreaInsetBottom: true,
  fixed: true,
  zIndex: 1000,
});

// TabBar项目列表
const tabBarItems = ref<TabBarItem[]>([
  { id: '1', name: 'home', title: '首页', icon: 'mdi:home', pageId: 'home' },
  { id: '2', name: 'search', title: '搜索', icon: 'mdi:magnify', pageId: 'search' },
  { id: '3', name: 'profile', title: '我的', icon: 'mdi:account', pageId: 'profile' },
]);

// 可用页面列表
const availablePages = ref([
  { id: 'home', title: '首页' },
  { id: 'search', title: '搜索页' },
  { id: 'profile', title: '个人中心' },
  { id: 'settings', title: '设置页' },
]);

// 可用图标列表
const availableIcons = [
  { value: 'mdi:home', label: '首页', icon: MdiHome },
  { value: 'mdi:magnify', label: '搜索', icon: MdiMagnify },
  { value: 'mdi:account', label: '用户', icon: MdiAccount },
  { value: 'mdi:cog', label: '设置', icon: MdiCog },
  { value: 'mdi:heart', label: '收藏', icon: MdiHeart },
];

// 表格列定义
const columns = [
  { title: '名称', dataIndex: 'name', key: 'name' },
  { title: '标题', dataIndex: 'title', key: 'title' },
  { title: '图标', dataIndex: 'icon', key: 'icon' },
  { title: '绑定页面', dataIndex: 'pageId', key: 'pageId' },
  { title: '操作', key: 'action', width: 120 },
];

// 添加TabBar项目
const handleAddItem = () => {
  const newItem: TabBarItem = {
    id: Date.now().toString(),
    name: `tab_${tabBarItems.value.length + 1}`,
    title: '新标签',
    icon: 'mdi:home',
    pageId: availablePages.value[0]?.id || '',
  };
  tabBarItems.value.push(newItem);
};

// 删除TabBar项目
const handleDeleteItem = (id: string) => {
  const index = tabBarItems.value.findIndex(item => item.id === id);
  if (index > -1) {
    tabBarItems.value.splice(index, 1);
  }
};

// 编辑TabBar项目
const handleEditItem = (id: string) => {
  console.log('编辑项目:', id);
};

// 获取页面标题
const getPageTitle = (pageId: string) => {
  const page = availablePages.value.find(p => p.id === pageId);
  return page?.title || pageId;
};

// 获取图标组件
const getIconComponent = (iconValue: string) => {
  const iconItem = availableIcons.find(icon => icon.value === iconValue);
  return iconItem?.icon || MdiHome;
};

// 预览样式
const previewStyle = computed(() => ({
  position: 'relative',
  width: '375px',
  height: '60px',
  backgroundColor: tabBarConfig.value.backgroundColor,
  borderTop: tabBarConfig.value.borderTop ? '1px solid #e8e8e8' : 'none',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-around',
  margin: '20px auto',
  borderRadius: '8px',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
}));
</script>

<template>
  <div class="tabbar-designer">
    <div class="designer-content">
      <Tabs v-model:activeKey="activeTab" type="card">
        <!-- TabBar配置 -->
        <TabPane key="config" tab="基础配置">
          <div class="config-section">
            <Card title="TabBar设置" size="small">
              <Form layout="vertical">
                <FormItem label="位置">
                  <Select v-model:value="tabBarConfig.position">
                    <Option value="bottom">底部</Option>
                    <Option value="top">顶部</Option>
                  </Select>
                </FormItem>
                
                <FormItem label="背景颜色">
                  <ColorPicker v-model:value="tabBarConfig.backgroundColor" />
                </FormItem>
                
                <FormItem label="激活颜色">
                  <ColorPicker v-model:value="tabBarConfig.activeColor" />
                </FormItem>
                
                <FormItem label="非激活颜色">
                  <ColorPicker v-model:value="tabBarConfig.inactiveColor" />
                </FormItem>
                
                <FormItem label="层级">
                  <InputNumber 
                    v-model:value="tabBarConfig.zIndex" 
                    :min="1" 
                    :max="9999" 
                  />
                </FormItem>
                
                <FormItem>
                  <Space direction="vertical">
                    <Switch 
                      v-model:checked="tabBarConfig.borderTop"
                      checked-children="显示上边框"
                      un-checked-children="隐藏上边框"
                    />
                    <Switch 
                      v-model:checked="tabBarConfig.fixed"
                      checked-children="固定定位"
                      un-checked-children="相对定位"
                    />
                    <Switch 
                      v-model:checked="tabBarConfig.safeAreaInsetBottom"
                      checked-children="适配安全区域"
                      un-checked-children="不适配安全区域"
                    />
                  </Space>
                </FormItem>
              </Form>
            </Card>
          </div>
        </TabPane>

        <!-- TabBar项目管理 -->
        <TabPane key="items" tab="项目管理">
          <div class="items-section">
            <Card 
              title="TabBar项目" 
              size="small"
              :extra="h(Button, { 
                type: 'primary', 
                size: 'small',
                icon: h(MdiPlus),
                onClick: handleAddItem 
              }, () => '添加项目')"
            >
              <Table
                :dataSource="tabBarItems"
                :columns="columns"
                :pagination="false"
                size="small"
                rowKey="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'icon'">
                    <Space>
                      <component :is="getIconComponent(record.icon)" />
                      <span>{{ record.icon }}</span>
                    </Space>
                  </template>
                  
                  <template v-else-if="column.key === 'pageId'">
                    {{ getPageTitle(record.pageId) }}
                  </template>
                  
                  <template v-else-if="column.key === 'action'">
                    <Space>
                      <Button
                        type="text"
                        size="small"
                        :icon="h(MdiPencil)"
                        @click="handleEditItem(record.id)"
                      >
                        编辑
                      </Button>
                      <Button
                        type="text"
                        size="small"
                        danger
                        :icon="h(MdiDelete)"
                        @click="handleDeleteItem(record.id)"
                      >
                        删除
                      </Button>
                    </Space>
                  </template>
                </template>
              </Table>
            </Card>
          </div>
        </TabPane>

        <!-- 预览 -->
        <TabPane key="preview" tab="预览效果">
          <div class="preview-section">
            <Card title="TabBar预览" size="small">
              <div class="preview-container">
                <div class="phone-mockup">
                  <div class="phone-screen">
                    <div class="screen-content">
                      <div class="content-placeholder">
                        <h3>页面内容区域</h3>
                        <p>TabBar将显示在页面底部</p>
                      </div>
                    </div>
                    
                    <!-- TabBar预览 -->
                    <div class="tabbar-preview" :style="previewStyle">
                      <div
                        v-for="item in tabBarItems"
                        :key="item.id"
                        class="tab-item"
                        :style="{
                          color: item.id === '1' ? tabBarConfig.activeColor : tabBarConfig.inactiveColor
                        }"
                      >
                        <div class="tab-icon">
                          <component :is="getIconComponent(item.icon)" />
                        </div>
                        <div class="tab-title">{{ item.title }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </TabPane>
      </Tabs>
    </div>
  </div>
</template>

<style scoped>
.tabbar-designer {
  height: 100%;
  padding: 16px;
  background: #f5f5f5;
}

.designer-content {
  height: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.config-section,
.items-section,
.preview-section {
  padding: 16px;
  height: calc(100vh - 200px);
  overflow-y: auto;
}

.preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.phone-mockup {
  width: 375px;
  height: 667px;
  background: #000;
  border-radius: 20px;
  padding: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.phone-screen {
  width: 100%;
  height: 100%;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.screen-content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.content-placeholder {
  text-align: center;
  color: #8c8c8c;
}

.content-placeholder h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #595959;
}

.content-placeholder p {
  margin: 0;
  font-size: 14px;
}

.tabbar-preview {
  flex-shrink: 0;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
  padding: 8px 4px;
  transition: color 0.2s;
}

.tab-icon {
  font-size: 20px;
  line-height: 1;
}

.tab-title {
  font-size: 10px;
  line-height: 1;
  text-align: center;
}

/* 滚动条样式 */
.config-section::-webkit-scrollbar,
.items-section::-webkit-scrollbar,
.preview-section::-webkit-scrollbar {
  width: 6px;
}

.config-section::-webkit-scrollbar-track,
.items-section::-webkit-scrollbar-track,
.preview-section::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.config-section::-webkit-scrollbar-thumb,
.items-section::-webkit-scrollbar-thumb,
.preview-section::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.config-section::-webkit-scrollbar-thumb:hover,
.items-section::-webkit-scrollbar-thumb:hover,
.preview-section::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
</style>
