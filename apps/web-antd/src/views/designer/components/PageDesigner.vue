<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Layout, Card, Tabs, Empty, Button, Space } from 'ant-design-vue';
import { MdiPlus, MdiWidgets, MdiViewDashboard } from '@vben/icons';

import ComponentPanel from './ComponentPanel.vue';
import DesignCanvas from './DesignCanvas.vue';

// 导入共享组件库的渲染器
import { ComponentRenderer } from '@vben/lowcode/core';

const { LayoutSider, LayoutContent } = Layout;
const { TabPane } = Tabs;

interface Props {
  selectedComponent?: any;
}

interface Emits {
  (e: 'component-select', component: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 当前激活的标签页
const activeTab = ref('components');

// 组件面板是否折叠
const componentPanelCollapsed = ref(false);

// 画布状态
const canvasState = ref({
  components: [],
  selectedComponentId: null,
  scale: 1,
  showGrid: true,
});

// 处理组件选择
const handleComponentSelect = (component: any) => {
  canvasState.value.selectedComponentId = component.id;
  emit('component-select', component);
};

// 处理组件拖拽到画布
const handleComponentDrop = (componentType: string, position: { x: number; y: number }) => {
  const newComponent = {
    id: `${componentType}_${Date.now()}`,
    type: componentType,
    props: {},
    style: {
      position: 'absolute',
      left: position.x + 'px',
      top: position.y + 'px',
    },
  };
  
  canvasState.value.components.push(newComponent);
  handleComponentSelect(newComponent);
};

// 处理组件删除
const handleComponentDelete = (componentId: string) => {
  const index = canvasState.value.components.findIndex(c => c.id === componentId);
  if (index > -1) {
    canvasState.value.components.splice(index, 1);
    if (canvasState.value.selectedComponentId === componentId) {
      canvasState.value.selectedComponentId = null;
      emit('component-select', null);
    }
  }
};

onMounted(() => {
  console.log('页面设计器初始化');
});
</script>

<template>
  <div class="page-designer">
    <Layout class="designer-layout">
      <!-- 左侧组件面板 -->
      <LayoutSider
        v-model:collapsed="componentPanelCollapsed"
        class="component-panel-sider"
        :width="280"
        :collapsed-width="50"
        theme="light"
        collapsible
      >
        <div class="component-panel-header">
          <Tabs v-model:activeKey="activeTab" size="small">
            <TabPane key="components" tab="组件">
              <template #tab>
                <span>
                  <MdiWidgets style="margin-right: 4px;" />
                  组件
                </span>
              </template>
            </TabPane>
            <TabPane key="outline" tab="大纲">
              <template #tab>
                <span>
                  <MdiViewDashboard style="margin-right: 4px;" />
                  大纲
                </span>
              </template>
            </TabPane>
          </Tabs>
        </div>

        <div class="component-panel-content">
          <!-- 组件面板 -->
          <div v-if="activeTab === 'components'" class="components-tab">
            <ComponentPanel 
              v-if="!componentPanelCollapsed"
              @component-drag="handleComponentDrop"
            />
            <div v-else class="collapsed-panel">
              <div class="collapsed-icon">
                <MdiWidgets />
              </div>
            </div>
          </div>

          <!-- 大纲面板 -->
          <div v-if="activeTab === 'outline'" class="outline-tab">
            <div v-if="!componentPanelCollapsed" class="outline-content">
              <div class="outline-header">
                <span class="outline-title">页面结构</span>
              </div>
              
              <div v-if="canvasState.components.length === 0" class="outline-empty">
                <Empty 
                  :image="Empty.PRESENTED_IMAGE_SIMPLE"
                  description="暂无组件"
                />
              </div>
              
              <div v-else class="outline-tree">
                <div
                  v-for="component in canvasState.components"
                  :key="component.id"
                  class="outline-item"
                  :class="{ active: canvasState.selectedComponentId === component.id }"
                  @click="handleComponentSelect(component)"
                >
                  <span class="outline-item-name">{{ component.type }}</span>
                  <Button
                    type="text"
                    size="small"
                    danger
                    @click.stop="handleComponentDelete(component.id)"
                  >
                    删除
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </LayoutSider>

      <!-- 右侧画布区域 -->
      <LayoutContent class="canvas-area">
        <div class="canvas-header">
          <div class="canvas-title">
            <h3>设计画布</h3>
            <span class="canvas-info">拖拽组件到画布进行设计</span>
          </div>
          
          <div class="canvas-tools">
            <Space>
              <Button size="small">
                网格: {{ canvasState.showGrid ? '开' : '关' }}
              </Button>
              <Button size="small">
                缩放: {{ Math.round(canvasState.scale * 100) }}%
              </Button>
            </Space>
          </div>
        </div>

        <div class="canvas-container">
          <DesignCanvas
            :components="canvasState.components"
            :selected-component-id="canvasState.selectedComponentId"
            :scale="canvasState.scale"
            :show-grid="canvasState.showGrid"
            @component-select="handleComponentSelect"
            @component-drop="handleComponentDrop"
            @component-delete="handleComponentDelete"
          />
        </div>
      </LayoutContent>
    </Layout>
  </div>
</template>

<style scoped>
.page-designer {
  height: 100%;
  background: #fff;
}

.designer-layout {
  height: 100%;
}

.component-panel-sider {
  border-right: 1px solid #e8e8e8;
}

.component-panel-header {
  padding: 16px 16px 0 16px;
  border-bottom: 1px solid #f0f0f0;
}

.component-panel-content {
  height: calc(100% - 60px);
  overflow: hidden;
}

.components-tab,
.outline-tab {
  height: 100%;
}

.collapsed-panel {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.collapsed-icon {
  font-size: 24px;
  color: #bfbfbf;
}

.outline-content {
  height: 100%;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.outline-header {
  margin-bottom: 16px;
}

.outline-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.outline-empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.outline-tree {
  flex: 1;
  overflow-y: auto;
}

.outline-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.outline-item:hover {
  background: #f5f5f5;
}

.outline-item.active {
  background: #e6f7ff;
  border: 1px solid #1890ff;
}

.outline-item-name {
  font-size: 13px;
  color: #262626;
}

.canvas-area {
  background: #f5f5f5;
  display: flex;
  flex-direction: column;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #e8e8e8;
}

.canvas-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.canvas-info {
  font-size: 12px;
  color: #8c8c8c;
  margin-left: 8px;
}

.canvas-container {
  flex: 1;
  padding: 16px;
  overflow: hidden;
}
</style>
