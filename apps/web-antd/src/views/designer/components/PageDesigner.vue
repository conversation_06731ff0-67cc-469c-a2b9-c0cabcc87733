<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { LayoutSider, LayoutContent,Layout, Card, Tabs, Empty, Button, Space } from 'ant-design-vue';
import { MdiPlus, MdiWidgets, MdiViewDashboard } from '@vben/icons';
import draggable from 'vuedraggable';

import ComponentPanel from './ComponentPanel.vue';
import DesignCanvas from './DesignCanvas.vue';

// 导入低代码核心组件
import { componentRegistry, ComponentRenderer } from '@vben/lowcode/core';

// const { TabPane } = Tabs;

interface Props {
  selectedComponent?: any;
}

interface Emits {
  (e: 'component-select', component: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 当前激活的标签页
const activeTab = ref('components');

// 组件面板是否折叠
const componentPanelCollapsed = ref(false);

// 画布状态
const canvasState = ref({
  components: [],
  selectedComponentId: null,
  scale: 1,
  showGrid: true,
});

// 处理组件选择
const handleComponentSelect = (component: any) => {
  canvasState.value.selectedComponentId = component.id;
  emit('component-select', component);
};

// 处理组件拖拽到画布
const handleComponentDrop = (componentType: string, position: { x: number; y: number }) => {
  console.log('PageDesigner: 接收到组件拖拽事件:', componentType, position);

  // 从组件注册中心获取组件定义
  const componentDef = componentRegistry.getComponentDefinition(componentType);
  console.log('PageDesigner: 获取到的组件定义:', componentDef);

  const defaultProps = componentDef?.defaultProps || {};

  const newComponent = {
    id: `${componentType}_${Date.now()}`,
    type: componentType,
    props: { ...defaultProps },
    style: {
      position: 'absolute',
      left: position.x + 'px',
      top: position.y + 'px',
      ...componentDef?.defaultProps?.style,
    },
    children: [],
  };

  console.log('PageDesigner: 创建的新组件:', newComponent);
  canvasState.value.components.push(newComponent);
  console.log('PageDesigner: 当前画布组件数量:', canvasState.value.components.length);

  handleComponentSelect(newComponent);
};

// 处理组件删除
const handleComponentDelete = (componentId: string) => {
  const index = canvasState.value.components.findIndex(c => c.id === componentId);
  if (index > -1) {
    canvasState.value.components.splice(index, 1);
    if (canvasState.value.selectedComponentId === componentId) {
      canvasState.value.selectedComponentId = null;
      emit('component-select', null);
    }
  }
};

// 处理组件更新
const handleComponentUpdate = (componentId: string, updates: any) => {
  const index = canvasState.value.components.findIndex(c => c.id === componentId);
  if (index > -1) {
    canvasState.value.components[index] = {
      ...canvasState.value.components[index],
      ...updates,
    };
  }
};

// 处理 vuedraggable 的组件添加事件
const handleComponentAdd = (event: any) => {
  console.log('PageDesigner: vuedraggable 添加组件', event);
  // vuedraggable 会自动处理组件添加，我们只需要确保组件有正确的ID
  const addedComponent = event.added?.element;
  if (addedComponent && !addedComponent.id) {
    addedComponent.id = `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
};

// 处理 vuedraggable 的组件变化事件
const handleComponentChange = (event: any) => {
  console.log('PageDesigner: vuedraggable 组件变化', event);
  // 可以在这里处理组件顺序变化等逻辑
};

onMounted(() => {
  console.log('页面设计器初始化');
});
</script>

<template>
  <div class="flex h-full">
    <!-- 左侧组件面板 -->
    <div class="w-80 border-r bg-white flex flex-col">
      <!-- 面板标签 -->
      <div class="border-b">
        <Tabs v-model:activeKey="activeTab" size="small">
          <TabPane key="components" tab="组件" />
          <TabPane key="outline" tab="大纲" />
        </Tabs>
      </div>

      <!-- 面板内容 -->
      <div class="flex-1 overflow-hidden">
        <!-- 组件面板 -->
        <div v-if="activeTab === 'components'" class="h-full">
          <ComponentPanel @component-drag="handleComponentDrop" />
        </div>

        <!-- 大纲面板 -->
        <div v-if="activeTab === 'outline'" class="h-full p-4">
          <div class="mb-4">
            <span class="text-sm font-medium text-gray-700">页面结构</span>
          </div>

          <div v-if="canvasState.components.length === 0" class="flex items-center justify-center h-32">
            <Empty
              :image="Empty.PRESENTED_IMAGE_SIMPLE"
              description="暂无组件"
            />
          </div>

          <div v-else class="space-y-2">
            <div
              v-for="component in canvasState.components"
              :key="component.id"
              class="flex items-center justify-between p-2 rounded border cursor-pointer hover:bg-gray-50"
              :class="{ 'bg-blue-50 border-blue-200': canvasState.selectedComponentId === component.id }"
              @click="handleComponentSelect(component)"
            >
              <span class="text-sm">{{ component.type }}</span>
              <Button
                type="text"
                size="small"
                danger
                @click.stop="handleComponentDelete(component.id)"
              >
                删除
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧画布区域 -->
    <div class="flex-1 flex flex-col">
      <!-- 画布头部 -->
      <div class="h-16 px-6 border-b bg-white flex items-center justify-between">
        <div>
          <h3 class="text-lg font-semibold text-gray-800">设计画布</h3>
          <span class="text-sm text-gray-500">拖拽组件到画布进行设计</span>
        </div>

        <div>
          <Space>
            <Button size="small">
              网格: {{ canvasState.showGrid ? '开' : '关' }}
            </Button>
            <Button size="small">
              缩放: {{ Math.round(canvasState.scale * 100) }}%
            </Button>
          </Space>
        </div>
      </div>

      <!-- 画布容器 -->
      <div class="flex-1 overflow-auto p-6 bg-gray-50">
        <div class="flex justify-center items-start min-h-full py-5">
          <!-- 手机模拟器框架 -->
          <div class="phone-frame">
            <!-- 手机顶部刘海 -->
            <div class="phone-notch"></div>

            <!-- 画布内容区域 - 使用 vuedraggable -->
            <div class="canvas-content">
              <draggable
                v-model="canvasState.components"
                group="components"
                item-key="id"
                class="min-h-96 w-full"
                :animation="200"
                @add="handleComponentAdd"
                @change="handleComponentChange"
              >
                <template #item="{ element: component }">
                  <div
                    class="mb-3 p-3 bg-white border rounded cursor-pointer hover:border-blue-300 transition-colors"
                    :class="{ 'border-blue-500 shadow-sm': canvasState.selectedComponentId === component.id }"
                    @click="handleComponentSelect(component)"
                  >
                    <ComponentRenderer
                      :id="component.id"
                      :type="component.type"
                      :component-props="component.props || {}"
                      :children="component.children || []"
                    />

                    <!-- 选中状态的删除按钮 -->
                    <div v-if="canvasState.selectedComponentId === component.id" class="mt-2 text-right">
                      <Button size="small" danger @click.stop="handleComponentDelete(component.id)">
                        删除
                      </Button>
                    </div>
                  </div>
                </template>

                <!-- 空状态 -->
                <template #header>
                  <div v-if="canvasState.components.length === 0" class="flex flex-col items-center justify-center h-96 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                    <div class="text-4xl mb-4">📱</div>
                    <h3 class="text-lg font-medium mb-2">开始设计你的页面</h3>
                    <p class="text-sm">从左侧组件面板拖拽组件到这里</p>
                  </div>
                </template>
              </draggable>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 手机模拟器框架样式 - 保持不变 */
.phone-frame {
  width: 375px;
  min-height: 667px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  position: relative;
  overflow: visible;
}

.phone-notch {
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #ccc;
  border-radius: 2px;
}

.canvas-content {
  padding: 20px;
  padding-top: 30px;
  min-height: 600px;
  box-sizing: border-box;
  position: relative;
}
</style>
