<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { LayoutSider, LayoutContent,Layout, Card, Tabs, Empty, Button, Space } from 'ant-design-vue';
import { MdiPlus, MdiWidgets, MdiViewDashboard } from '@vben/icons';

import ComponentPanel from './ComponentPanel.vue';
import DesignCanvas from './DesignCanvas.vue';

// 导入低代码核心组件
import { componentRegistry, ComponentRenderer } from '@vben/lowcode/core';

// const { TabPane } = Tabs;

interface Props {
  selectedComponent?: any;
}

interface Emits {
  (e: 'component-select', component: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 当前激活的标签页
const activeTab = ref('components');

// 组件面板是否折叠
const componentPanelCollapsed = ref(false);

// 画布状态
const canvasState = ref({
  components: [],
  selectedComponentId: null,
  scale: 1,
  showGrid: true,
});

// 处理组件选择
const handleComponentSelect = (component: any) => {
  canvasState.value.selectedComponentId = component.id;
  emit('component-select', component);
};

// 处理组件拖拽到画布
const handleComponentDrop = (componentType: string, position: { x: number; y: number }) => {
  console.log('PageDesigner: 接收到组件拖拽事件:', componentType, position);

  // 从组件注册中心获取组件定义
  const componentDef = componentRegistry.getComponentDefinition(componentType);
  console.log('PageDesigner: 获取到的组件定义:', componentDef);

  const defaultProps = componentDef?.defaultProps || {};

  const newComponent = {
    id: `${componentType}_${Date.now()}`,
    type: componentType,
    props: { ...defaultProps },
    style: {
      position: 'absolute',
      left: position.x + 'px',
      top: position.y + 'px',
      ...componentDef?.defaultProps?.style,
    },
    children: [],
  };

  console.log('PageDesigner: 创建的新组件:', newComponent);
  canvasState.value.components.push(newComponent);
  console.log('PageDesigner: 当前画布组件数量:', canvasState.value.components.length);

  handleComponentSelect(newComponent);
};

// 处理组件删除
const handleComponentDelete = (componentId: string) => {
  const index = canvasState.value.components.findIndex(c => c.id === componentId);
  if (index > -1) {
    canvasState.value.components.splice(index, 1);
    if (canvasState.value.selectedComponentId === componentId) {
      canvasState.value.selectedComponentId = null;
      emit('component-select', null);
    }
  }
};

// 处理组件更新
const handleComponentUpdate = (componentId: string, updates: any) => {
  const index = canvasState.value.components.findIndex(c => c.id === componentId);
  if (index > -1) {
    canvasState.value.components[index] = {
      ...canvasState.value.components[index],
      ...updates,
    };
  }
};

// 处理画布拖拽放置
const handleCanvasDrop = (event: DragEvent) => {
  event.preventDefault();
  console.log('PageDesigner: 直接处理拖拽放置');

  const componentType = event.dataTransfer?.getData('component-type');
  if (!componentType) return;

  // 计算相对于画布的位置
  const rect = (event.target as HTMLElement).getBoundingClientRect();
  const position = {
    x: event.clientX - rect.left,
    y: event.clientY - rect.top,
  };

  handleComponentDrop(componentType, position);
};

// 获取组件位置样式
const getComponentPositionStyle = (component: any) => {
  return {
    position: 'absolute',
    left: (component.style?.left || '0px'),
    top: (component.style?.top || '0px'),
    cursor: 'pointer',
    border: '1px solid #ddd',
    padding: '8px',
    background: 'white',
    borderRadius: '4px',
    minWidth: '60px',
    minHeight: '32px',
  };
};

onMounted(() => {
  console.log('页面设计器初始化');
});
</script>

<template>
  <div class="page-designer">
    <Layout class="designer-layout">
      <!-- 左侧组件面板 -->
      <LayoutSider
        v-model:collapsed="componentPanelCollapsed"
        class="component-panel-sider"
        :width="280"
        :collapsed-width="50"
        theme="light"
        collapsible
      >
        <div class="component-panel-header">
          <Tabs v-model:activeKey="activeTab" size="small">
            <TabPane key="components" tab="组件">
              <template #tab>
                <span>
                  <MdiWidgets style="margin-right: 4px;" />
                  组件
                </span>
              </template>
            </TabPane>
            <TabPane key="outline" tab="大纲">
              <template #tab>
                <span>
                  <MdiViewDashboard style="margin-right: 4px;" />
                  大纲
                </span>
              </template>
            </TabPane>
          </Tabs>
        </div>

        <div class="component-panel-content">
          <!-- 组件面板 -->
          <div v-if="activeTab === 'components'" class="components-tab">
            <ComponentPanel 
              v-if="!componentPanelCollapsed"
              @component-drag="handleComponentDrop"
            />
            <div v-else class="collapsed-panel">
              <div class="collapsed-icon">
                <MdiWidgets />
              </div>
            </div>
          </div>

          <!-- 大纲面板 -->
          <div v-if="activeTab === 'outline'" class="outline-tab">
            <div v-if="!componentPanelCollapsed" class="outline-content">
              <div class="outline-header">
                <span class="outline-title">页面结构</span>
              </div>
              
              <div v-if="canvasState.components.length === 0" class="outline-empty">
                <Empty 
                  :image="Empty.PRESENTED_IMAGE_SIMPLE"
                  description="暂无组件"
                />
              </div>
              
              <div v-else class="outline-tree">
                <div
                  v-for="component in canvasState.components"
                  :key="component.id"
                  class="outline-item"
                  :class="{ active: canvasState.selectedComponentId === component.id }"
                  @click="handleComponentSelect(component)"
                >
                  <span class="outline-item-name">{{ component.type }}</span>
                  <Button
                    type="text"
                    size="small"
                    danger
                    @click.stop="handleComponentDelete(component.id)"
                  >
                    删除
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </LayoutSider>

      <!-- 右侧画布区域 -->
      <LayoutContent class="canvas-area">
        <div class="canvas-header">
          <div class="canvas-title">
            <h3>设计画布</h3>
            <span class="canvas-info">拖拽组件到画布进行设计</span>
          </div>
          
          <div class="canvas-tools">
            <Space>
              <Button size="small">
                网格: {{ canvasState.showGrid ? '开' : '关' }}
              </Button>
              <Button size="small">
                缩放: {{ Math.round(canvasState.scale * 100) }}%
              </Button>
            </Space>
          </div>
        </div>

        <div class="canvas-container">
          <!-- 直接显示画布，不依赖DesignCanvas组件 -->
          <div class="simple-canvas" @drop="handleCanvasDrop" @dragover.prevent>
            <!-- 手机模拟器框架 -->
            <div class="phone-frame">
              <!-- 手机顶部刘海 -->
              <div class="phone-notch"></div>

              <!-- 画布内容区域 -->
              <div class="canvas-content">
                <div v-if="canvasState.components.length === 0" class="canvas-empty">
                  <div class="empty-icon">📱</div>
                  <h3>开始设计你的页面</h3>
                  <p>从左侧组件面板拖拽组件到这里</p>
                </div>

                <!-- 渲染实际组件 -->
                <div v-for="component in canvasState.components" :key="component.id"
                     class="canvas-component"
                     :style="getComponentPositionStyle(component)"
                     @click="handleComponentSelect(component)">
                  <ComponentRenderer
                    :id="component.id"
                    :type="component.type"
                    :component-props="component.props || {}"
                    :children="component.children || []"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </LayoutContent>
    </Layout>
  </div>
</template>

<style scoped>
/* 页面设计器主容器 */
.page-designer {
  height: 100%;
  background: transparent;
  position: relative;
}

.designer-layout {
  height: 100%;
  background: transparent;
}

/* 组件面板侧边栏 */
.component-panel-sider {
  border: none;
  border-right: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
}

.component-panel-header {
  padding: 20px 20px 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.component-panel-content {
  height: calc(100% - 80px);
  overflow: hidden;
}

/* 标签页优化 */
.component-panel-header :deep(.ant-tabs) {
  margin: 0;
}

.component-panel-header :deep(.ant-tabs-tab) {
  padding: 8px 16px;
  margin: 0 4px;
  border-radius: 8px;
  transition: all 0.3s ease;
  background: transparent;
  border: 1px solid transparent;
}

.component-panel-header :deep(.ant-tabs-tab:hover) {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.2);
}

.component-panel-header :deep(.ant-tabs-tab-active) {
  background: rgba(102, 126, 234, 0.15);
  border-color: rgba(102, 126, 234, 0.3);
  color: #667eea;
  font-weight: 600;
}

.component-panel-header :deep(.ant-tabs-ink-bar) {
  display: none;
}

.components-tab,
.outline-tab {
  height: 100%;
  padding: 16px;
}

/* 折叠面板 */
.collapsed-panel {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: rgba(255, 255, 255, 0.5);
}

.collapsed-icon {
  font-size: 32px;
  color: #a0aec0;
  opacity: 0.6;
}

/* 大纲内容 */
.outline-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.outline-header {
  margin-bottom: 16px;
}

.outline-title {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.outline-empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #718096;
  font-size: 14px;
}

.outline-tree {
  flex: 1;
  overflow-y: auto;
  padding-right: 4px;
}

/* 大纲项目 */
.outline-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  margin-bottom: 6px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.outline-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.outline-item:hover {
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(102, 126, 234, 0.2);
  transform: translateX(4px);
}

.outline-item:hover::before {
  transform: scaleY(1);
}

.outline-item.active {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.3);
  transform: translateX(4px);
}

.outline-item.active::before {
  transform: scaleY(1);
}

.outline-item-name {
  font-size: 13px;
  color: #2d3748;
  font-weight: 500;
  transition: color 0.3s ease;
}

.outline-item.active .outline-item-name {
  color: #667eea;
  font-weight: 600;
}

/* 画布区域 */
.canvas-area {
  background: transparent;
  display: flex;
  flex-direction: column;
  position: relative;
}

.canvas-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(102, 126, 234, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(118, 75, 162, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.canvas-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 10;
}

.canvas-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
  gap: 8px;
}

.canvas-info {
  font-size: 12px;
  color: #718096;
  background: rgba(102, 126, 234, 0.1);
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.canvas-container {
  flex: 1;
  padding: 24px;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

/* 简化的画布样式 */
.simple-canvas {
  width: 100%;
  height: 100%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.phone-frame {
  width: 375px;
  height: 667px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  position: relative;
  overflow: hidden;
}

.phone-notch {
  position: absolute;
  top: 8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: #ccc;
  border-radius: 2px;
}

.canvas-content {
  padding: 20px;
  padding-top: 30px;
  height: 100%;
  box-sizing: border-box;
  position: relative;
}

.canvas-empty {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #666;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.canvas-empty h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.canvas-empty p {
  margin: 0;
  font-size: 14px;
}

.canvas-component {
  transition: all 0.2s ease;
}

.canvas-component:hover {
  border-color: #1890ff !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

/* 滚动条美化 */
.outline-tree {
  scrollbar-width: thin;
  scrollbar-color: rgba(102, 126, 234, 0.2) transparent;
}

.outline-tree::-webkit-scrollbar {
  width: 4px;
}

.outline-tree::-webkit-scrollbar-track {
  background: transparent;
}

.outline-tree::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.2);
  border-radius: 2px;
}

.outline-tree::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.3);
}
</style>
