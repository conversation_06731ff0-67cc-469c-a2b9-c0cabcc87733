<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Collapse, Card, Input, Empty } from 'ant-design-vue';
import {
  MdiButton,
  MdiTextBox,
  MdiImage,
  MdiFormTextbox,
  MdiToggleSwitch,
  MdiRadioboxMarked,
  MdiCheckboxMarked,
  MdiViewList,
  MdiGrid,
  MdiTabUnselected
} from '@vben/icons';
import draggable from 'vuedraggable';

// 导入低代码核心组件
import { componentRegistry } from '@vben/lowcode/core';

const { CollapsePanel } = Collapse;

interface ComponentItem {
  type: string;
  name: string;
  icon: any;
  category: string;
  description?: string;
}

interface Emits {
  (e: 'component-drag', componentType: string, position: { x: number; y: number }): void;
}

const emit = defineEmits<Emits>();

// 搜索关键词
const searchKeyword = ref('');

// 激活的面板 - 动态设置为所有分类
const activeKeys = ref<string[]>([]);

// 在组件挂载后设置默认展开的分类
onMounted(() => {
  console.log('ComponentPanel: 组件面板初始化');

  // 立即检查组件注册状态
  const checkComponents = () => {
    const allComponents = componentRegistry.getAllComponentDefinitions();
    console.log('ComponentPanel: 检查 - 注册的组件数量:', allComponents.length);

    if (allComponents.length === 0) {
      console.warn('ComponentPanel: ⚠️ 没有找到任何注册的组件！');
      // 如果没有组件，1秒后再次检查
      setTimeout(checkComponents, 1000);
    } else {
      console.log('ComponentPanel: ✅ 找到组件:', allComponents.map(c => c.type));

      // 设置所有分类为展开状态
      const categories = componentRegistry.getAllCategories();
      activeKeys.value = categories;
      console.log('ComponentPanel: 设置展开的分类:', activeKeys.value);
    }
  };

  // 立即执行检查
  checkComponents();
});

// 图标映射
const iconMap: Record<string, any> = {
  button: MdiButton,
  text: MdiTextBox,
  image: MdiImage,
  input: MdiFormTextbox,
  switch: MdiToggleSwitch,
  radio: MdiRadioboxMarked,
  checkbox: MdiCheckboxMarked,
  container: MdiGrid,
  list: MdiViewList,
  tabbar: MdiTabUnselected,
};

// 分类映射
const categoryMap: Record<string, string> = {
  basic: 'basic',
  form: 'form',
  layout: 'layout',
  business: 'business',
  data: 'data',
};

// 从组件注册中心获取组件库数据
const componentLibrary = computed<ComponentItem[]>(() => {
  const allComponents = componentRegistry.getAllComponentDefinitions();
  console.log('ComponentPanel: 获取到的组件数量:', allComponents.length);
  console.log('ComponentPanel: 组件详情:', allComponents);

  const mappedComponents = allComponents.map(component => {
    const mappedComponent = {
      type: component.type,
      name: component.label,
      icon: iconMap[component.type] || MdiButton,
      category: component.category.toLowerCase(),
      description: component.label,
    };
    console.log(`ComponentPanel: 映射组件 ${component.type}:`, mappedComponent);
    return mappedComponent;
  });

  console.log('ComponentPanel: 最终组件库数据:', mappedComponents);
  return mappedComponents;
});

// 组件分类 - 根据实际注册的组件分类动态生成
const componentCategories = computed(() => {
  const categories = componentRegistry.getAllCategories();
  console.log('ComponentPanel: 获取到的分类:', categories);

  return categories.map(category => ({
    key: category,
    title: getCategoryTitle(category),
    description: getCategoryDescription(category),
  }));
});

// 获取分类标题
const getCategoryTitle = (category: string) => {
  const titleMap: Record<string, string> = {
    basic: '基础组件',
    form: '表单组件',
    layout: '布局组件',
    navigation: '导航组件',
    business: '业务组件',
    '演示组件': '演示组件',
  };
  return titleMap[category] || category;
};

// 获取分类描述
const getCategoryDescription = (category: string) => {
  const descMap: Record<string, string> = {
    basic: '常用的基础UI组件',
    form: '用于数据输入的表单控件',
    layout: '用于页面布局的容器组件',
    navigation: '用于页面导航的组件',
    business: '业务相关的组件',
    '演示组件': '用于演示的组件',
  };
  return descMap[category] || `${category}相关组件`;
};

// 根据分类和搜索过滤组件
const getComponentsByCategory = (category: string) => {
  return componentLibrary.value.filter(component => {
    const matchCategory = component.category === category;
    const matchSearch = !searchKeyword.value ||
      component.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      component.type.toLowerCase().includes(searchKeyword.value.toLowerCase());
    return matchCategory && matchSearch;
  });
};

// 处理组件拖拽开始
const handleDragStart = (event: DragEvent, componentType: string) => {
  console.log('ComponentPanel: 开始拖拽组件:', componentType);
  if (event.dataTransfer) {
    event.dataTransfer.setData('component-type', componentType);
    event.dataTransfer.effectAllowed = 'copy';
    console.log('ComponentPanel: 设置拖拽数据成功');
  }
};

// 处理组件点击（模拟拖拽到画布中心）
const handleComponentClick = (componentType: string) => {
  console.log('ComponentPanel: 点击组件:', componentType);
  // 模拟拖拽到画布中心位置
  const position = { x: 200, y: 150 };
  emit('component-drag', componentType, position);
};

// 克隆组件用于拖拽
const cloneComponent = (component: ComponentItem) => {
  console.log('ComponentPanel: 克隆组件用于拖拽:', component.type);

  // 获取组件定义
  const definition = componentRegistry.getComponentDefinition(component.type);

  return {
    id: `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type: component.type,
    props: definition?.defaultProps || {},
    style: {},
    children: [],
  };
};


</script>

<template>
  <div class="h-full flex flex-col">
    <!-- 搜索框 -->
    <div class="p-4 border-b">
      <Input
        v-model:value="searchKeyword"
        placeholder="搜索组件..."
        allow-clear
      />
    </div>

    <!-- 组件分类 -->
    <div class="flex-1 overflow-auto">
      <!-- 如果没有任何组件分类，显示加载状态 -->
      <div v-if="componentCategories.length === 0" class="p-4">
        <div class="text-center py-8">
          <div class="text-2xl text-gray-400 mb-2">⏳</div>
          <div class="text-sm text-gray-500">正在加载组件...</div>
        </div>
      </div>

      <!-- 组件分类列表 -->
      <Collapse v-else v-model:activeKey="activeKeys" ghost>
        <CollapsePanel
          v-for="category in componentCategories"
          :key="category.key"
          :header="category.title"
        >
          <template #extra>
            <span class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
              {{ getComponentsByCategory(category.key).length }}
            </span>
          </template>

          <div class="grid grid-cols-1 gap-2">
            <div
              v-for="component in getComponentsByCategory(category.key)"
              :key="component.type"
              class="flex items-center p-3 border border-gray-200 rounded-lg cursor-move hover:bg-blue-50 hover:border-blue-300 transition-all"
              draggable="true"
              @dragstart="handleDragStart($event, component.type)"
              @click="handleComponentClick(component.type)"
            >
              <div class="w-6 h-6 mr-3 flex items-center justify-center">
                <component :is="component.icon" class="text-blue-500" />
              </div>
              <div class="flex-1">
                <div class="text-sm font-medium text-gray-900">{{ component.name }}</div>
                <div class="text-xs text-gray-500">{{ component.type }}</div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="getComponentsByCategory(category.key).length === 0" class="py-8">
              <Empty
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                description="暂无匹配的组件"
              />
            </div>
          </div>
        </CollapsePanel>
      </Collapse>
    </div>

    <!-- 快速组件列表 -->
    <div class="border-t border-gray-200 bg-gray-50">
      <div class="p-4">
        <h4 class="mb-3 text-sm font-semibold text-gray-800 flex items-center">
          <span class="mr-2">🚀</span>
          快速组件列表
        </h4>

        <div class="max-h-80 overflow-y-auto">
          <draggable
            :list="componentLibrary"
            group="components"
            item-key="type"
            :clone="cloneComponent"
            :sort="false"
            class="space-y-2"
          >
            <template #item="{ element: component }">
              <div
                class="flex items-center p-3 bg-white border border-gray-200 rounded-lg cursor-move hover:bg-blue-50 hover:border-blue-300 transition-all shadow-sm"
                @click="handleComponentClick(component.type)"
              >
                <div class="w-6 h-6 mr-3 flex items-center justify-center text-blue-500">
                  <component :is="component.icon" />
                </div>
                <div class="flex-1">
                  <div class="text-sm font-medium text-gray-900">{{ component.name }}</div>
                  <div class="text-xs text-gray-500">{{ component.type }}</div>
                </div>
              </div>
            </template>
          </draggable>

          <div v-if="componentLibrary.length === 0" class="text-center py-8 text-gray-500">
            暂无组件
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<!-- 使用 Tailwind CSS，删除自定义样式 -->


