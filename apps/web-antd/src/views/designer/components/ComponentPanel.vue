<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Collapse, Card, Input, Empty } from 'ant-design-vue';
import {
  MdiButton,
  MdiTextBox,
  MdiImage,
  MdiFormTextbox,
  MdiToggleSwitch,
  MdiRadioboxMarked,
  MdiCheckboxMarked,
  MdiViewList,
  MdiGrid,
  MdiTabUnselected
} from '@vben/icons';

// 导入共享组件库
import { AllComponents } from '@vben/lowcode/components';

const { CollapsePanel } = Collapse;

interface ComponentItem {
  type: string;
  name: string;
  icon: any;
  category: string;
  description?: string;
}

interface Emits {
  (e: 'component-drag', componentType: string, position: { x: number; y: number }): void;
}

const emit = defineEmits<Emits>();

// 搜索关键词
const searchKeyword = ref('');

// 激活的面板
const activeKeys = ref(['basic', 'form', 'layout']);

// 图标映射
const iconMap: Record<string, any> = {
  button: MdiButton,
  text: MdiTextBox,
  image: MdiImage,
  input: MdiFormTextbox,
  switch: MdiToggleSwitch,
  radio: MdiRadioboxMarked,
  checkbox: MdiCheckboxMarked,
  container: MdiGrid,
  list: MdiViewList,
  tabbar: MdiTabUnselected,
};

// 分类映射
const categoryMap: Record<string, string> = {
  basic: 'basic',
  form: 'form',
  layout: 'layout',
  business: 'business',
  data: 'data',
};

// 从共享组件库生成组件库数据
const componentLibrary = computed<ComponentItem[]>(() => {
  return AllComponents.map(component => ({
    type: component.type,
    name: component.name,
    icon: iconMap[component.type] || MdiButton,
    category: categoryMap[component.category] || 'basic',
    description: component.description || component.name,
  }));
});

// 组件分类
const componentCategories = [
  { key: 'basic', title: '基础组件', description: '常用的基础UI组件' },
  { key: 'form', title: '表单组件', description: '用于数据输入的表单控件' },
  { key: 'layout', title: '布局组件', description: '用于页面布局的容器组件' },
];

// 根据分类和搜索过滤组件
const getComponentsByCategory = (category: string) => {
  return componentLibrary.value.filter(component => {
    const matchCategory = component.category === category;
    const matchSearch = !searchKeyword.value ||
      component.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      component.type.toLowerCase().includes(searchKeyword.value.toLowerCase());
    return matchCategory && matchSearch;
  });
};

// 处理组件拖拽开始
const handleDragStart = (event: DragEvent, componentType: string) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('component-type', componentType);
    event.dataTransfer.effectAllowed = 'copy';
  }
};

// 处理组件点击（模拟拖拽到画布中心）
const handleComponentClick = (componentType: string) => {
  // 模拟拖拽到画布中心位置
  const position = { x: 200, y: 150 };
  emit('component-drag', componentType, position);
};

onMounted(() => {
  console.log('组件面板初始化');
});
</script>

<template>
  <div class="component-panel">
    <!-- 搜索框 -->
    <div class="search-section">
      <Input
        v-model:value="searchKeyword"
        placeholder="搜索组件..."
        allow-clear
      />
    </div>

    <!-- 组件分类 -->
    <div class="components-section">
      <Collapse v-model:activeKey="activeKeys" ghost>
        <CollapsePanel
          v-for="category in componentCategories"
          :key="category.key"
          :header="category.title"
        >
          <template #extra>
            <span class="category-count">
              {{ getComponentsByCategory(category.key).length }}
            </span>
          </template>

          <div class="category-description">
            {{ category.description }}
          </div>

          <div class="component-grid">
            <div
              v-for="component in getComponentsByCategory(category.key)"
              :key="component.type"
              class="component-item"
              draggable="true"
              @dragstart="handleDragStart($event, component.type)"
              @click="handleComponentClick(component.type)"
            >
              <div class="component-icon">
                <component :is="component.icon" />
              </div>
              <div class="component-info">
                <div class="component-name">{{ component.name }}</div>
                <div class="component-description">{{ component.description }}</div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="getComponentsByCategory(category.key).length === 0" class="category-empty">
              <Empty 
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                description="暂无匹配的组件"
              />
            </div>
          </div>
        </CollapsePanel>
      </Collapse>
    </div>
  </div>
</template>

<style scoped>
.component-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.search-section {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.components-section {
  flex: 1;
  overflow-y: auto;
  padding: 8px 16px;
}

.category-count {
  background: #f0f0f0;
  color: #595959;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 11px;
  min-width: 16px;
  text-align: center;
}

.category-description {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 12px;
  padding: 0 4px;
}

.component-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.component-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s;
  background: #fff;
}

.component-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.1);
  transform: translateY(-1px);
}

.component-item:active {
  cursor: grabbing;
  transform: translateY(0);
}

.component-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: #f5f5f5;
  border-radius: 4px;
  font-size: 16px;
  color: #595959;
  flex-shrink: 0;
}

.component-item:hover .component-icon {
  background: #e6f7ff;
  color: #1890ff;
}

.component-info {
  flex: 1;
  min-width: 0;
}

.component-name {
  font-size: 13px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.component-description {
  font-size: 11px;
  color: #8c8c8c;
  line-height: 1.4;
}

.category-empty {
  padding: 20px 0;
  text-align: center;
}

/* 拖拽时的样式 */
.component-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

/* 滚动条样式 */
.components-section::-webkit-scrollbar {
  width: 4px;
}

.components-section::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 2px;
}

.components-section::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 2px;
}

.components-section::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
</style>
