<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { Collapse, Card, Input, Empty } from 'ant-design-vue';
import {
  MdiButton,
  MdiTextBox,
  MdiImage,
  MdiFormTextbox,
  MdiToggleSwitch,
  MdiRadioboxMarked,
  MdiCheckboxMarked,
  MdiViewList,
  MdiGrid,
  MdiTabUnselected
} from '@vben/icons';
import draggable from 'vuedraggable';

// 导入低代码核心组件
import { componentRegistry } from '@vben/lowcode/core';

const { CollapsePanel } = Collapse;

interface ComponentItem {
  type: string;
  name: string;
  icon: any;
  category: string;
  description?: string;
}

interface Emits {
  (e: 'component-drag', componentType: string, position: { x: number; y: number }): void;
}

const emit = defineEmits<Emits>();

// 搜索关键词
const searchKeyword = ref('');

// 激活的面板 - 动态设置为所有分类
const activeKeys = ref<string[]>([]);

// 在组件挂载后设置默认展开的分类
onMounted(() => {
  console.log('ComponentPanel: 组件面板初始化');

  // 延迟检查组件注册状态
  setTimeout(() => {
    const allComponents = componentRegistry.getAllComponentDefinitions();
    console.log('ComponentPanel: 延迟检查 - 注册的组件数量:', allComponents.length);

    if (allComponents.length === 0) {
      console.warn('ComponentPanel: ⚠️ 没有找到任何注册的组件！');
    } else {
      console.log('ComponentPanel: ✅ 找到组件:', allComponents.map(c => c.type));

      // 设置所有分类为展开状态
      const categories = componentRegistry.getAllCategories();
      activeKeys.value = categories;
      console.log('ComponentPanel: 设置展开的分类:', activeKeys.value);
    }
  }, 2000);
});

// 图标映射
const iconMap: Record<string, any> = {
  button: MdiButton,
  text: MdiTextBox,
  image: MdiImage,
  input: MdiFormTextbox,
  switch: MdiToggleSwitch,
  radio: MdiRadioboxMarked,
  checkbox: MdiCheckboxMarked,
  container: MdiGrid,
  list: MdiViewList,
  tabbar: MdiTabUnselected,
};

// 分类映射
const categoryMap: Record<string, string> = {
  basic: 'basic',
  form: 'form',
  layout: 'layout',
  business: 'business',
  data: 'data',
};

// 从组件注册中心获取组件库数据
const componentLibrary = computed<ComponentItem[]>(() => {
  const allComponents = componentRegistry.getAllComponentDefinitions();
  console.log('ComponentPanel: 获取到的组件数量:', allComponents.length);
  console.log('ComponentPanel: 组件详情:', allComponents);

  const mappedComponents = allComponents.map(component => {
    const mappedComponent = {
      type: component.type,
      name: component.label,
      icon: iconMap[component.type] || MdiButton,
      category: component.category.toLowerCase(),
      description: component.label,
    };
    console.log(`ComponentPanel: 映射组件 ${component.type}:`, mappedComponent);
    return mappedComponent;
  });

  console.log('ComponentPanel: 最终组件库数据:', mappedComponents);
  return mappedComponents;
});

// 组件分类 - 根据实际注册的组件分类动态生成
const componentCategories = computed(() => {
  const categories = componentRegistry.getAllCategories();
  console.log('ComponentPanel: 获取到的分类:', categories);

  return categories.map(category => ({
    key: category,
    title: getCategoryTitle(category),
    description: getCategoryDescription(category),
  }));
});

// 获取分类标题
const getCategoryTitle = (category: string) => {
  const titleMap: Record<string, string> = {
    basic: '基础组件',
    form: '表单组件',
    layout: '布局组件',
    navigation: '导航组件',
    business: '业务组件',
    '演示组件': '演示组件',
  };
  return titleMap[category] || category;
};

// 获取分类描述
const getCategoryDescription = (category: string) => {
  const descMap: Record<string, string> = {
    basic: '常用的基础UI组件',
    form: '用于数据输入的表单控件',
    layout: '用于页面布局的容器组件',
    navigation: '用于页面导航的组件',
    business: '业务相关的组件',
    '演示组件': '用于演示的组件',
  };
  return descMap[category] || `${category}相关组件`;
};

// 根据分类和搜索过滤组件
const getComponentsByCategory = (category: string) => {
  return componentLibrary.value.filter(component => {
    const matchCategory = component.category === category;
    const matchSearch = !searchKeyword.value ||
      component.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      component.type.toLowerCase().includes(searchKeyword.value.toLowerCase());
    return matchCategory && matchSearch;
  });
};

// 处理组件拖拽开始
const handleDragStart = (event: DragEvent, componentType: string) => {
  console.log('ComponentPanel: 开始拖拽组件:', componentType);
  if (event.dataTransfer) {
    event.dataTransfer.setData('component-type', componentType);
    event.dataTransfer.effectAllowed = 'copy';
    console.log('ComponentPanel: 设置拖拽数据成功');
  }
};

// 处理组件点击（模拟拖拽到画布中心）
const handleComponentClick = (componentType: string) => {
  console.log('ComponentPanel: 点击组件:', componentType);
  // 模拟拖拽到画布中心位置
  const position = { x: 200, y: 150 };
  emit('component-drag', componentType, position);
};

// 克隆组件用于拖拽
const cloneComponent = (component: ComponentItem) => {
  console.log('ComponentPanel: 克隆组件用于拖拽:', component.type);

  // 获取组件定义
  const definition = componentRegistry.getComponentDefinition(component.type);

  return {
    id: `component_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    type: component.type,
    props: definition?.defaultProps || {},
    style: {},
    children: [],
  };
};


</script>

<template>
  <div class="component-panel">
    <!-- 搜索框 -->
    <div class="search-section">
      <Input
        v-model:value="searchKeyword"
        placeholder="搜索组件..."
        allow-clear
      />
    </div>

    <!-- 调试信息面板 -->
    <div style="background: #fff2e8; border: 1px solid #ffb366; padding: 12px; margin: 16px; border-radius: 6px;">
      <div style="font-weight: bold; color: #d46b08; margin-bottom: 8px;">🔧 调试信息</div>
      <div style="font-size: 12px; color: #8c4a00;">
        <div>总组件数: {{ componentLibrary.length }}</div>
        <div>组件类型: {{ componentLibrary.map(c => c.type).join(', ') }}</div>
        <div>分类数量: {{ componentCategories.length }}</div>
        <div>分类列表: {{ componentCategories.map(c => c.key).join(', ') }}</div>
        <div>activeKeys: {{ activeKeys.join(', ') }}</div>
      </div>
    </div>

    <!-- 组件分类 -->
    <div class="components-section">
      <Collapse v-model:activeKey="activeKeys" ghost>
        <CollapsePanel
          v-for="category in componentCategories"
          :key="category.key"
          :header="category.title"
        >
          <template #extra>
            <span class="category-count">
              {{ getComponentsByCategory(category.key).length }}
            </span>
          </template>

          <div class="category-description">
            {{ category.description }}
          </div>

          <!-- 调试信息 -->
          <div style="background: #e6f7ff; padding: 8px; margin: 8px 0; border-radius: 4px; font-size: 12px;">
            🔍 调试: {{ category.key }} 分类有 {{ getComponentsByCategory(category.key).length }} 个组件
          </div>

          <div class="component-grid">
            <div
              v-for="component in getComponentsByCategory(category.key)"
              :key="component.type"
              class="component-item"
              draggable="true"
              @dragstart="handleDragStart($event, component.type)"
              @click="handleComponentClick(component.type)"
            >
              <div class="component-icon">
                <component :is="component.icon" />
              </div>
              <div class="component-info">
                <div class="component-name">{{ component.name }}</div>
                <div class="component-description">{{ component.description }}</div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="getComponentsByCategory(category.key).length === 0" class="category-empty">
              <Empty 
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                description="暂无匹配的组件"
              />
            </div>
          </div>
        </CollapsePanel>
      </Collapse>
    </div>

    <!-- 使用 vuedraggable 的组件列表 -->
    <div style="border-top: 2px solid #f0f0f0; margin-top: 20px; padding-top: 20px;">
      <h4 style="margin: 0 16px 16px 16px; color: #333;">🚀 快速组件列表</h4>

      <div style="padding: 0 16px; max-height: 300px; overflow-y: auto;">
        <draggable
          :list="componentLibrary"
          group="components"
          item-key="type"
          :clone="cloneComponent"
          :sort="false"
          class="component-draggable-list"
        >
          <template #item="{ element: component }">
            <div
              class="draggable-component-item"
              @click="handleComponentClick(component.type)"
            >
              <div class="component-item-icon">
                <component :is="component.icon" />
              </div>
              <div class="component-item-content">
                <div class="component-item-name">{{ component.name }}</div>
                <div class="component-item-type">{{ component.type }}</div>
              </div>
            </div>
          </template>
        </draggable>

        <div v-if="componentLibrary.length === 0" style="text-align: center; padding: 20px; color: #999;">
          暂无组件
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.component-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
}

.search-section {
  padding: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  flex-shrink: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.search-section :deep(.ant-input) {
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  font-size: 14px;
  padding: 8px 16px;
}

.search-section :deep(.ant-input:hover) {
  border-color: rgba(102, 126, 234, 0.3);
  background: rgba(255, 255, 255, 0.95);
}

.search-section :deep(.ant-input:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
  background: rgba(255, 255, 255, 1);
}

.components-section {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px;
  background: transparent;
}

/* 折叠面板优化 */
.components-section :deep(.ant-collapse) {
  background: transparent;
  border: none;
}

.components-section :deep(.ant-collapse-item) {
  margin-bottom: 16px;
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  overflow: hidden;
}

.components-section :deep(.ant-collapse-header) {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
  border-radius: 12px 12px 0 0;
  padding: 16px 20px;
  font-weight: 600;
  color: #2d3748;
}

.components-section :deep(.ant-collapse-content) {
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  background: rgba(255, 255, 255, 0.9);
}

.components-section :deep(.ant-collapse-content-box) {
  padding: 16px 20px;
}

.category-count {
  background: rgba(102, 126, 234, 0.15);
  color: #667eea;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.category-description {
  font-size: 12px;
  color: #718096;
  margin-bottom: 16px;
  padding: 0 4px;
  font-weight: 500;
}

.component-grid {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.component-item {
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 14px 16px;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  cursor: grab;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.component-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.component-item:hover {
  border-color: rgba(102, 126, 234, 0.3);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 1);
}

.component-item:hover::before {
  transform: scaleY(1);
}

.component-item:active {
  cursor: grabbing;
  transform: translateY(0) scale(0.98);
}

.component-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: rgba(102, 126, 234, 0.1);
  border-radius: 8px;
  font-size: 18px;
  color: #4a5568;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.component-item:hover .component-icon {
  background: rgba(102, 126, 234, 0.2);
  color: #667eea;
  transform: scale(1.1);
}

.component-info {
  flex: 1;
  min-width: 0;
}

.component-name {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
  transition: color 0.3s ease;
}

.component-item:hover .component-name {
  color: #667eea;
}

.component-description {
  font-size: 12px;
  color: #718096;
  line-height: 1.4;
  font-weight: 500;
}

.category-empty {
  padding: 40px 20px;
  text-align: center;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 12px;
  border: 2px dashed rgba(102, 126, 234, 0.2);
  color: #718096;
  font-weight: 500;
}

/* 拖拽时的样式 */
.component-item.dragging {
  opacity: 0.6;
  transform: rotate(3deg) scale(0.95);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.3);
}

/* 滚动条美化 */
.components-section {
  scrollbar-width: thin;
  scrollbar-color: rgba(102, 126, 234, 0.2) transparent;
}

.components-section::-webkit-scrollbar {
  width: 4px;
}

.components-section::-webkit-scrollbar-track {
  background: transparent;
}

.components-section::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.2);
  border-radius: 2px;
}

.components-section::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.3);
}
</style>
